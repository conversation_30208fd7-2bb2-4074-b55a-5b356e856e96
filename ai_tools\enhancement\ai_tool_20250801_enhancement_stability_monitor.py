#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 添加监控、错误恢复和资源管理机制，确保24/7稳定运行
清理条件: 功能被替代时删除
"""

"""
长期稳定性保障组件 - 24/7运行监控
===============================

确保千川自动化系统长期稳定运行：
1. 系统资源监控和告警
2. 自动错误恢复机制
3. 浏览器进程健康检查
4. 数据库连接池监控
5. Redis连接状态监控
6. 工作流执行监控

技术特性：
- 实时资源监控
- 自动故障恢复
- 智能告警系统
- 性能优化建议
- 历史数据分析

创建时间: 2025-08-01
版本: v2.0 - 稳定性保障版
"""

import os
import psutil
import time
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass
from contextlib import contextmanager
from loguru import logger

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import Campaign, AdAccount, PlatformCreative
from ai_tool_20250801_enhancement_database_sync import get_database_sync_manager


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    process_count: int
    thread_count: int
    db_connections: int
    redis_connections: int
    browser_processes: int


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    component: str
    status: str  # 'healthy', 'warning', 'critical'
    message: str
    metrics: Dict[str, Any]
    timestamp: datetime


class SystemResourceMonitor:
    """系统资源监控器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.thresholds = {
            'cpu_warning': 80.0,
            'cpu_critical': 95.0,
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'disk_warning': 85.0,
            'disk_critical': 95.0
        }
        
        logger.info("📊 系统资源监控器初始化完成")
    
    def get_system_metrics(self) -> SystemMetrics:
        """获取系统指标"""
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # 进程和线程数
            process_count = len(psutil.pids())
            thread_count = threading.active_count()
            
            # 数据库连接数（估算）
            db_connections = self._estimate_db_connections()
            
            # Redis连接数（估算）
            redis_connections = self._estimate_redis_connections()
            
            # 浏览器进程数
            browser_processes = self._count_browser_processes()
            
            return SystemMetrics(
                timestamp=datetime.now(timezone.utc),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                process_count=process_count,
                thread_count=thread_count,
                db_connections=db_connections,
                redis_connections=redis_connections,
                browser_processes=browser_processes
            )
            
        except Exception as e:
            logger.error(f"❌ 获取系统指标失败: {e}")
            return None
    
    def _estimate_db_connections(self) -> int:
        """估算数据库连接数"""
        try:
            # 通过进程名称估算PostgreSQL连接
            postgres_connections = 0
            for proc in psutil.process_iter(['name']):
                if 'postgres' in proc.info['name'].lower():
                    postgres_connections += 1
            return postgres_connections
        except:
            return 0
    
    def _estimate_redis_connections(self) -> int:
        """估算Redis连接数"""
        try:
            # 通过网络连接估算Redis连接
            redis_connections = 0
            for conn in psutil.net_connections():
                if conn.laddr and conn.laddr.port == 6379:
                    redis_connections += 1
            return redis_connections
        except:
            return 0
    
    def _count_browser_processes(self) -> int:
        """统计浏览器进程数"""
        try:
            browser_count = 0
            browser_names = ['chrome', 'chromium', 'firefox', 'playwright']
            
            for proc in psutil.process_iter(['name']):
                proc_name = proc.info['name'].lower()
                if any(browser in proc_name for browser in browser_names):
                    browser_count += 1
                    
            return browser_count
        except:
            return 0
    
    def check_resource_health(self, metrics: SystemMetrics) -> List[HealthCheckResult]:
        """检查资源健康状态"""
        
        results = []
        
        # CPU检查
        if metrics.cpu_percent >= self.thresholds['cpu_critical']:
            status = 'critical'
            message = f"CPU使用率过高: {metrics.cpu_percent:.1f}%"
        elif metrics.cpu_percent >= self.thresholds['cpu_warning']:
            status = 'warning'
            message = f"CPU使用率较高: {metrics.cpu_percent:.1f}%"
        else:
            status = 'healthy'
            message = f"CPU使用率正常: {metrics.cpu_percent:.1f}%"
        
        results.append(HealthCheckResult(
            component='cpu',
            status=status,
            message=message,
            metrics={'cpu_percent': metrics.cpu_percent},
            timestamp=metrics.timestamp
        ))
        
        # 内存检查
        if metrics.memory_percent >= self.thresholds['memory_critical']:
            status = 'critical'
            message = f"内存使用率过高: {metrics.memory_percent:.1f}%"
        elif metrics.memory_percent >= self.thresholds['memory_warning']:
            status = 'warning'
            message = f"内存使用率较高: {metrics.memory_percent:.1f}%"
        else:
            status = 'healthy'
            message = f"内存使用率正常: {metrics.memory_percent:.1f}%"
        
        results.append(HealthCheckResult(
            component='memory',
            status=status,
            message=message,
            metrics={'memory_percent': metrics.memory_percent},
            timestamp=metrics.timestamp
        ))
        
        # 磁盘检查
        if metrics.disk_percent >= self.thresholds['disk_critical']:
            status = 'critical'
            message = f"磁盘使用率过高: {metrics.disk_percent:.1f}%"
        elif metrics.disk_percent >= self.thresholds['disk_warning']:
            status = 'warning'
            message = f"磁盘使用率较高: {metrics.disk_percent:.1f}%"
        else:
            status = 'healthy'
            message = f"磁盘使用率正常: {metrics.disk_percent:.1f}%"
        
        results.append(HealthCheckResult(
            component='disk',
            status=status,
            message=message,
            metrics={'disk_percent': metrics.disk_percent},
            timestamp=metrics.timestamp
        ))
        
        # 浏览器进程检查
        if metrics.browser_processes > 10:
            status = 'warning'
            message = f"浏览器进程过多: {metrics.browser_processes} 个"
        elif metrics.browser_processes > 20:
            status = 'critical'
            message = f"浏览器进程严重过多: {metrics.browser_processes} 个"
        else:
            status = 'healthy'
            message = f"浏览器进程正常: {metrics.browser_processes} 个"
        
        results.append(HealthCheckResult(
            component='browser_processes',
            status=status,
            message=message,
            metrics={'browser_processes': metrics.browser_processes},
            timestamp=metrics.timestamp
        ))
        
        return results


class AutoRecoveryManager:
    """自动恢复管理器"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        self.recovery_actions = {
            'high_cpu': self._recover_high_cpu,
            'high_memory': self._recover_high_memory,
            'too_many_browsers': self._recover_browser_overload,
            'database_issues': self._recover_database_issues,
            'redis_issues': self._recover_redis_issues
        }
        
        logger.info("🔧 自动恢复管理器初始化完成")
    
    def execute_recovery_actions(self, health_results: List[HealthCheckResult]) -> List[Dict[str, Any]]:
        """执行恢复操作"""
        
        recovery_results = []
        
        for result in health_results:
            if result.status in ['warning', 'critical']:
                logger.warning(f"⚠️ 检测到问题: {result.component} - {result.message}")
                
                # 确定恢复操作
                recovery_action = self._determine_recovery_action(result)
                
                if recovery_action:
                    logger.info(f"🔧 执行恢复操作: {recovery_action}")
                    
                    try:
                        success, message = self.recovery_actions[recovery_action]()
                        
                        recovery_results.append({
                            'component': result.component,
                            'action': recovery_action,
                            'success': success,
                            'message': message,
                            'timestamp': datetime.now(timezone.utc)
                        })
                        
                        if success:
                            logger.success(f"✅ 恢复操作成功: {message}")
                        else:
                            logger.error(f"❌ 恢复操作失败: {message}")
                            
                    except Exception as e:
                        logger.error(f"❌ 恢复操作异常: {e}")
                        recovery_results.append({
                            'component': result.component,
                            'action': recovery_action,
                            'success': False,
                            'message': f"恢复操作异常: {str(e)}",
                            'timestamp': datetime.now(timezone.utc)
                        })
        
        return recovery_results
    
    def _determine_recovery_action(self, health_result: HealthCheckResult) -> Optional[str]:
        """确定恢复操作"""
        
        component_action_map = {
            'cpu': 'high_cpu',
            'memory': 'high_memory',
            'browser_processes': 'too_many_browsers'
        }
        
        return component_action_map.get(health_result.component)
    
    def _recover_high_cpu(self) -> tuple[bool, str]:
        """恢复高CPU使用率"""
        try:
            # 降低进程优先级
            current_process = psutil.Process()
            current_process.nice(10)  # 降低优先级
            
            # 建议减少并发任务
            logger.info("💡 建议: 减少Celery worker并发数")
            
            return True, "已降低进程优先级，建议减少并发任务"
        except Exception as e:
            return False, f"CPU恢复失败: {str(e)}"
    
    def _recover_high_memory(self) -> tuple[bool, str]:
        """恢复高内存使用率"""
        try:
            # 强制垃圾回收
            import gc
            gc.collect()
            
            # 建议重启浏览器会话
            logger.info("💡 建议: 重启浏览器会话释放内存")
            
            return True, "已执行垃圾回收，建议重启浏览器会话"
        except Exception as e:
            return False, f"内存恢复失败: {str(e)}"
    
    def _recover_browser_overload(self) -> tuple[bool, str]:
        """恢复浏览器过载"""
        try:
            # 清理僵尸浏览器进程
            killed_count = 0
            browser_names = ['chrome', 'chromium']
            
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(browser in proc_name for browser in browser_names):
                        # 检查进程是否响应
                        if not proc.is_running():
                            proc.kill()
                            killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return True, f"已清理 {killed_count} 个僵尸浏览器进程"
        except Exception as e:
            return False, f"浏览器恢复失败: {str(e)}"
    
    def _recover_database_issues(self) -> tuple[bool, str]:
        """恢复数据库问题"""
        try:
            # 测试数据库连接
            with database_session() as db:
                db.execute("SELECT 1")
            
            return True, "数据库连接正常"
        except Exception as e:
            return False, f"数据库恢复失败: {str(e)}"
    
    def _recover_redis_issues(self) -> tuple[bool, str]:
        """恢复Redis问题"""
        try:
            sync_manager = get_database_sync_manager(self.app_settings)
            stats = sync_manager.get_sync_statistics()
            
            if 'error' in stats:
                return False, f"Redis连接失败: {stats['error']}"
            
            return True, "Redis连接正常"
        except Exception as e:
            return False, f"Redis恢复失败: {str(e)}"


class StabilityMonitor:
    """稳定性监控主控制器"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        self.resource_monitor = SystemResourceMonitor()
        self.recovery_manager = AutoRecoveryManager(app_settings)
        
        self.config = {
            'monitor_interval': 60,  # 监控间隔60秒
            'auto_recovery_enabled': True,
            'alert_threshold': 'warning',
            'max_recovery_attempts': 3
        }
        
        self.is_running = False
        self.monitor_thread = None
        
        logger.info("🛡️ 稳定性监控器初始化完成")
    
    def start_monitoring(self):
        """启动监控"""
        if self.is_running:
            logger.warning("⚠️ 监控已在运行中")
            return
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("🚀 稳定性监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("🛑 稳定性监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 获取系统指标
                metrics = self.resource_monitor.get_system_metrics()
                
                if metrics:
                    # 健康检查
                    health_results = self.resource_monitor.check_resource_health(metrics)
                    
                    # 检查是否需要告警
                    critical_issues = [r for r in health_results if r.status == 'critical']
                    warning_issues = [r for r in health_results if r.status == 'warning']
                    
                    if critical_issues:
                        logger.error(f"🚨 发现 {len(critical_issues)} 个严重问题")
                        for issue in critical_issues:
                            logger.error(f"   - {issue.component}: {issue.message}")
                    
                    if warning_issues:
                        logger.warning(f"⚠️ 发现 {len(warning_issues)} 个警告问题")
                        for issue in warning_issues:
                            logger.warning(f"   - {issue.component}: {issue.message}")
                    
                    # 自动恢复
                    if self.config['auto_recovery_enabled'] and (critical_issues or warning_issues):
                        recovery_results = self.recovery_manager.execute_recovery_actions(health_results)
                        
                        if recovery_results:
                            successful_recoveries = [r for r in recovery_results if r['success']]
                            logger.info(f"🔧 执行了 {len(recovery_results)} 个恢复操作，{len(successful_recoveries)} 个成功")
                    
                    # 记录健康状态
                    self._log_health_status(metrics, health_results)
                
                # 等待下次监控
                time.sleep(self.config['monitor_interval'])
                
            except Exception as e:
                logger.error(f"❌ 监控循环异常: {e}")
                time.sleep(10)  # 异常时短暂等待
    
    def _log_health_status(self, metrics: SystemMetrics, health_results: List[HealthCheckResult]):
        """记录健康状态"""
        
        healthy_count = len([r for r in health_results if r.status == 'healthy'])
        total_count = len(health_results)
        
        logger.info(f"📊 系统健康状态: {healthy_count}/{total_count} 组件正常")
        logger.debug(f"   CPU: {metrics.cpu_percent:.1f}%, 内存: {metrics.memory_percent:.1f}%, 磁盘: {metrics.disk_percent:.1f}%")
        logger.debug(f"   浏览器进程: {metrics.browser_processes}, 线程: {metrics.thread_count}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        
        try:
            metrics = self.resource_monitor.get_system_metrics()
            
            if not metrics:
                return {'error': '无法获取系统指标'}
            
            health_results = self.resource_monitor.check_resource_health(metrics)
            
            return {
                'timestamp': metrics.timestamp.isoformat(),
                'metrics': {
                    'cpu_percent': metrics.cpu_percent,
                    'memory_percent': metrics.memory_percent,
                    'disk_percent': metrics.disk_percent,
                    'browser_processes': metrics.browser_processes,
                    'thread_count': metrics.thread_count
                },
                'health_status': {
                    'healthy': len([r for r in health_results if r.status == 'healthy']),
                    'warning': len([r for r in health_results if r.status == 'warning']),
                    'critical': len([r for r in health_results if r.status == 'critical'])
                },
                'is_monitoring': self.is_running
            }
            
        except Exception as e:
            return {'error': str(e)}


# 全局监控器实例
_stability_monitor = None
_monitor_lock = threading.Lock()


def get_stability_monitor(app_settings: Dict[str, Any]) -> StabilityMonitor:
    """获取稳定性监控器单例"""
    
    global _stability_monitor
    
    if _stability_monitor is None:
        with _monitor_lock:
            if _stability_monitor is None:
                _stability_monitor = StabilityMonitor(app_settings)
    
    return _stability_monitor


# 便捷函数
def start_stability_monitoring(app_settings: Dict[str, Any]):
    """启动稳定性监控"""
    monitor = get_stability_monitor(app_settings)
    monitor.start_monitoring()


def stop_stability_monitoring(app_settings: Dict[str, Any]):
    """停止稳定性监控"""
    monitor = get_stability_monitor(app_settings)
    monitor.stop_monitoring()


def get_system_health_status(app_settings: Dict[str, Any]) -> Dict[str, Any]:
    """获取系统健康状态"""
    monitor = get_stability_monitor(app_settings)
    return monitor.get_current_status()


# 测试函数
def test_stability_monitor():
    """测试稳定性监控"""
    
    logger.info("🧪 开始测试稳定性监控")
    
    try:
        from qianchuan_aw.utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        
        monitor = get_stability_monitor(app_settings)
        
        # 获取当前状态
        status = monitor.get_current_status()
        logger.info(f"📊 当前系统状态: {status}")
        
        # 启动监控（测试模式）
        monitor.config['monitor_interval'] = 5  # 5秒间隔用于测试
        monitor.start_monitoring()
        
        logger.info("⏳ 监控运行中，等待30秒...")
        time.sleep(30)
        
        monitor.stop_monitoring()
        logger.success("✅ 稳定性监控测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)


if __name__ == "__main__":
    test_stability_monitor()
