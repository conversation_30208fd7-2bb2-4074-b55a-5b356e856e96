#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 安全地重构工作流目录结构，包含完整的Git备份和回滚机制
清理条件: 重构完成并稳定运行后可归档
"""

"""
工作流目录重构工具 (带Git备份)
============================

安全地将分散的工作流相关代码统一到工作流目录下，包含完整的Git备份和回滚机制。

功能特性：
1. 自动Git提交当前状态作为备份
2. 创建专门的重构分支
3. 渐进式迁移，确保系统稳定性
4. 自动创建新目录结构
5. 智能文件迁移和重命名
6. 自动更新导入路径
7. 完整的回滚机制

创建时间: 2025-08-03
版本: v1.0 - Git安全重构版
"""

import os
import subprocess
import shutil
import re
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from loguru import logger


class GitSafeWorkflowReorganizer:
    """带Git安全机制的工作流目录重构器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.workflows_dir = self.project_root / "src" / "qianchuan_aw" / "workflows"
        self.ai_tools_dir = self.project_root / "ai_tools"
        
        # Git相关
        self.original_branch = None
        self.backup_branch = None
        self.reorganization_branch = None
        
        # 新目录结构定义
        self.new_structure = {
            'core': ['scheduler.py', 'tasks.py'],
            'enhancement': [
                'ai_tool_20250801_enhancement_workflow_integration.py',
                'ai_tool_20250801_enhancement_plan_status_checker.py', 
                'ai_tool_20250801_enhancement_smart_appeal_service.py',
                'ai_tool_20250801_enhancement_thread_safe_appeal_service.py',
                'ai_tool_20250801_enhancement_stability_monitor.py'
            ],
            'appeal': [
                'smart_appeal_scheduler.py',
                'enhanced_appeal_workflow.py', 
                'batch_appeal_system.py',
                'appeal_and_monitor.py'
            ],
            'harvest': [
                'independent_material_harvest.py',
                'realtime_harvest_workflow.py',
                'fast_monitor.py'
            ],
            'reliability': [
                'fault_tolerance_workflow.py',
                'workflow_self_healing.py',
                'smart_stagnation_detector.py'
            ],
            'common': [
                'flexible_grouping.py'
            ]
        }
        
        # 文件重命名映射
        self.rename_mapping = {
            'ai_tool_20250801_enhancement_workflow_integration.py': 'workflow_integration.py',
            'ai_tool_20250801_enhancement_plan_status_checker.py': 'plan_status_checker.py',
            'ai_tool_20250801_enhancement_smart_appeal_service.py': 'smart_appeal_service.py',
            'ai_tool_20250801_enhancement_thread_safe_appeal_service.py': 'thread_safe_appeal_service.py',
            'ai_tool_20250801_enhancement_stability_monitor.py': 'stability_monitor.py'
        }
        
    def _run_git_command(self, command: List[str]) -> Tuple[bool, str]:
        """执行Git命令"""
        try:
            result = subprocess.run(
                command, 
                cwd=self.project_root, 
                capture_output=True, 
                text=True, 
                check=True
            )
            return True, result.stdout.strip()
        except subprocess.CalledProcessError as e:
            return False, e.stderr.strip()
            
    def check_git_status(self) -> bool:
        """检查Git状态"""
        logger.info("🔍 检查Git状态...")
        
        # 检查是否在Git仓库中
        success, output = self._run_git_command(['git', 'status', '--porcelain'])
        if not success:
            logger.error("❌ 当前目录不是Git仓库或Git不可用")
            return False
            
        # 获取当前分支
        success, branch = self._run_git_command(['git', 'branch', '--show-current'])
        if success:
            self.original_branch = branch
            logger.info(f"📍 当前分支: {self.original_branch}")
        else:
            logger.error("❌ 无法获取当前分支信息")
            return False
            
        # 检查是否有未提交的更改
        if output.strip():
            logger.warning("⚠️ 检测到未提交的更改:")
            for line in output.strip().split('\n'):
                logger.warning(f"   {line}")
            logger.info("💡 建议：先提交或暂存当前更改")
            
            # 询问是否继续
            response = input("是否要自动提交当前更改并继续? (y/N): ")
            if response.lower() == 'y':
                return self._commit_current_changes()
            else:
                logger.info("🛑 请先处理未提交的更改，然后重新运行")
                return False
        else:
            logger.success("✅ 工作目录干净，可以安全进行重构")
            
        return True
        
    def _commit_current_changes(self) -> bool:
        """提交当前更改"""
        logger.info("📝 自动提交当前更改...")
        
        # 添加所有更改
        success, _ = self._run_git_command(['git', 'add', '.'])
        if not success:
            logger.error("❌ 添加文件失败")
            return False
            
        # 提交更改
        commit_message = f"自动提交: 工作流重构前的状态保存 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        success, _ = self._run_git_command(['git', 'commit', '-m', commit_message])
        if not success:
            logger.error("❌ 提交失败")
            return False
            
        logger.success("✅ 当前更改已自动提交")
        return True
        
    def create_git_backup(self) -> bool:
        """创建Git备份"""
        logger.info("📦 创建Git备份...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_branch = f"backup/workflow_reorganization_{timestamp}"
        self.reorganization_branch = f"feature/workflow_reorganization_{timestamp}"
        
        # 创建备份分支
        success, _ = self._run_git_command(['git', 'checkout', '-b', self.backup_branch])
        if not success:
            logger.error("❌ 创建备份分支失败")
            return False
            
        logger.success(f"✅ 备份分支已创建: {self.backup_branch}")
        
        # 切换回原分支
        success, _ = self._run_git_command(['git', 'checkout', self.original_branch])
        if not success:
            logger.error("❌ 切换回原分支失败")
            return False
            
        # 创建重构工作分支
        success, _ = self._run_git_command(['git', 'checkout', '-b', self.reorganization_branch])
        if not success:
            logger.error("❌ 创建重构分支失败")
            return False
            
        logger.success(f"✅ 重构工作分支已创建: {self.reorganization_branch}")
        return True
        
    def create_new_structure(self) -> bool:
        """创建新的目录结构"""
        logger.info("🏗️ 创建新目录结构...")
        
        try:
            # 创建主要子目录
            for subdir in self.new_structure.keys():
                new_dir = self.workflows_dir / subdir
                new_dir.mkdir(exist_ok=True)
                
                # 创建 __init__.py
                init_file = new_dir / "__init__.py"
                if not init_file.exists():
                    init_content = f'"""{subdir.title()}工作流模块"""\n'
                    init_file.write_text(init_content)
                    
            # 创建legacy目录用于备份文件
            legacy_dir = self.workflows_dir / "legacy" / "backups"
            legacy_dir.mkdir(parents=True, exist_ok=True)
            (self.workflows_dir / "legacy" / "__init__.py").write_text('"""遗留文件"""\n')
            
            logger.success("✅ 新目录结构创建完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建目录结构失败: {e}")
            return False
            
    def migrate_files(self) -> bool:
        """迁移文件到新位置"""
        logger.info("📁 开始文件迁移...")
        
        try:
            # 迁移AI增强工具
            self._migrate_ai_enhancement_tools()
            
            # 重组现有工作流文件
            self._reorganize_existing_workflows()
            
            # 移动备份文件
            self._move_backup_files()
            
            logger.success("✅ 文件迁移完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 文件迁移失败: {e}")
            return False
            
    def _migrate_ai_enhancement_tools(self):
        """迁移AI增强工具"""
        logger.info("🔧 迁移AI增强工具...")
        
        enhancement_source = self.ai_tools_dir / "enhancement"
        enhancement_target = self.workflows_dir / "enhancement"
        
        for filename in self.new_structure['enhancement']:
            source_file = enhancement_source / filename
            if source_file.exists():
                # 确定目标文件名
                target_filename = self.rename_mapping.get(filename, filename)
                target_file = enhancement_target / target_filename
                
                # 复制文件
                shutil.copy2(source_file, target_file)
                logger.info(f"   📄 {filename} → {target_filename}")
                
                # 更新文件内容
                self._update_file_content(target_file)
                
    def _reorganize_existing_workflows(self):
        """重组现有工作流文件"""
        logger.info("📋 重组现有工作流文件...")
        
        for subdir, files in self.new_structure.items():
            if subdir == 'enhancement':  # 已经处理过
                continue
                
            target_dir = self.workflows_dir / subdir
            
            for filename in files:
                source_file = self.workflows_dir / filename
                if source_file.exists():
                    target_file = target_dir / filename
                    
                    # 移动文件
                    shutil.move(str(source_file), str(target_file))
                    logger.info(f"   📄 {filename} → {subdir}/{filename}")
                    
    def _move_backup_files(self):
        """移动备份文件到legacy目录"""
        logger.info("🗂️ 整理备份文件...")
        
        backup_target = self.workflows_dir / "legacy" / "backups"
        
        # 查找所有备份文件
        backup_patterns = [
            "*.backup*",
            "*_backup_*",
            "scheduler.backup_*",
            "tasks.backup_*"
        ]
        
        for pattern in backup_patterns:
            for backup_file in self.workflows_dir.glob(pattern):
                if backup_file.is_file():
                    target_file = backup_target / backup_file.name
                    shutil.move(str(backup_file), str(target_file))
                    logger.info(f"   📄 {backup_file.name} → legacy/backups/")
                    
    def _update_file_content(self, file_path: Path):
        """更新文件内容，修复导入路径"""
        logger.debug(f"🔧 更新文件内容: {file_path}")
        
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # 更新AI工具文件头部信息
            if 'AI生成文件信息' in content:
                content = re.sub(
                    r'文件类型: 长期工具',
                    '文件类型: 工作流增强组件',
                    content
                )
                content = re.sub(
                    r'创建目的: .*',
                    '创建目的: 工作流增强功能，已集成到统一工作流目录',
                    content
                )
                
            # 更新内部导入路径
            content = self._fix_internal_imports(content)
            
            file_path.write_text(content, encoding='utf-8')
            
        except Exception as e:
            logger.warning(f"⚠️ 更新文件内容失败 {file_path}: {e}")
            
    def _fix_internal_imports(self, content: str) -> str:
        """修复内部导入路径"""
        
        # 修复AI工具间的相互导入
        import_fixes = [
            (r'from ai_tool_20250801_enhancement_plan_status_checker import', 
             'from .plan_status_checker import'),
            (r'from ai_tool_20250801_enhancement_smart_appeal_service import',
             'from .smart_appeal_service import'),
            (r'from ai_tool_20250801_enhancement_thread_safe_appeal_service import',
             'from .thread_safe_appeal_service import'),
        ]
        
        for old_pattern, new_pattern in import_fixes:
            content = re.sub(old_pattern, new_pattern, content)
            
        return content
        
    def update_main_imports(self) -> bool:
        """更新主要文件中的导入路径"""
        logger.info("🔗 更新主要导入路径...")
        
        try:
            # 更新scheduler.py中的导入
            self._update_scheduler_imports()
            
            logger.success("✅ 导入路径更新完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新导入路径失败: {e}")
            return False
            
    def _update_scheduler_imports(self):
        """更新scheduler.py中的导入"""
        scheduler_file = self.workflows_dir / "core" / "scheduler.py"
        
        if scheduler_file.exists():
            content = scheduler_file.read_text(encoding='utf-8')
            
            # 替换AI工具导入为新的相对导入
            new_import = """        # 使用工作流增强集成器
        from ..enhancement.workflow_integration import handle_plan_submission_smart"""
            
            content = re.sub(
                r'        # 使用智能工作流集成器.*?from ai_tool_20250801_enhancement_workflow_integration import handle_plan_submission_smart',
                new_import,
                content,
                flags=re.DOTALL
            )
            
            scheduler_file.write_text(content, encoding='utf-8')
            logger.info("   ✅ scheduler.py 导入路径已更新")
            
    def commit_reorganization(self) -> bool:
        """提交重构更改"""
        logger.info("📝 提交重构更改...")
        
        # 添加所有更改
        success, _ = self._run_git_command(['git', 'add', '.'])
        if not success:
            logger.error("❌ 添加文件失败")
            return False
            
        # 提交更改
        commit_message = f"重构: 统一工作流目录结构\n\n- 将AI增强工具迁移到workflows/enhancement/\n- 按功能重组工作流文件\n- 更新导入路径\n- 清理备份文件到legacy目录"
        success, _ = self._run_git_command(['git', 'commit', '-m', commit_message])
        if not success:
            logger.error("❌ 提交重构更改失败")
            return False
            
        logger.success("✅ 重构更改已提交")
        return True
        
    def provide_rollback_instructions(self):
        """提供回滚说明"""
        logger.info("🔄 回滚说明:")
        logger.info("="*50)
        logger.info("如果重构后出现问题，可以使用以下命令回滚:")
        logger.info(f"1. 切换到备份分支: git checkout {self.backup_branch}")
        logger.info(f"2. 恢复到原分支: git checkout {self.original_branch}")
        logger.info(f"3. 重置到备份状态: git reset --hard {self.backup_branch}")
        logger.info("4. 删除重构分支: git branch -D " + self.reorganization_branch)
        logger.info("="*50)
        
    def execute_reorganization(self) -> bool:
        """执行完整的重构流程"""
        logger.info("🚀 开始工作流目录重构 (Git安全版)")
        logger.info("="*60)
        
        try:
            # 第一步：检查Git状态
            if not self.check_git_status():
                return False
                
            # 第二步：创建Git备份
            if not self.create_git_backup():
                return False
                
            # 第三步：创建新目录结构
            if not self.create_new_structure():
                return False
                
            # 第四步：迁移文件
            if not self.migrate_files():
                return False
                
            # 第五步：更新导入路径
            if not self.update_main_imports():
                return False
                
            # 第六步：提交更改
            if not self.commit_reorganization():
                return False
                
            logger.success("🎉 工作流目录重构完成！")
            logger.info(f"📍 当前分支: {self.reorganization_branch}")
            logger.info(f"📦 备份分支: {self.backup_branch}")
            
            # 提供回滚说明
            self.provide_rollback_instructions()
            
            logger.info("💡 建议：运行测试确保所有功能正常")
            logger.info("💡 如果测试通过，可以合并到主分支")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 重构过程失败: {e}")
            logger.info("🔄 建议使用Git回滚到安全状态")
            return False


def main():
    """主函数"""
    logger.info("🏗️ 工作流目录重构工具 (Git安全版)")
    
    # 获取项目根目录
    project_root = os.getcwd()
    
    # 创建重构器
    reorganizer = GitSafeWorkflowReorganizer(project_root)
    
    # 执行重构
    success = reorganizer.execute_reorganization()
    
    if success:
        logger.success("✅ 重构成功完成！")
    else:
        logger.error("❌ 重构失败，请检查日志")
        
    return success


if __name__ == "__main__":
    main()
