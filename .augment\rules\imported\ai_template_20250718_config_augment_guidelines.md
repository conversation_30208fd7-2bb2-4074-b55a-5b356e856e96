---
type: "always_apply"
---

# Augment User Guidelines - AI文件管理

## 千川自动化项目AI文件管理规范

### 核心规则
在千川自动化项目中，AI助手必须遵循严格的文件管理规范，防止临时文件污染项目结构。

### 1. 文件命名规范（强制）
所有AI生成文件必须使用格式：`ai_[类型]_[日期]_[用途]_[描述].[扩展名]`

**类型前缀**：
- `ai_temp_`: 临时文件（7天清理）
- `ai_tool_`: 长期工具（永久保留）  
- `ai_report_`: 分析报告（30天清理）
- `ai_template_`: 配置模板（永久保留）

**示例**：
```
ai_temp_20250718_cleanup_docs.py
ai_report_20250718_audit_analysis.md
ai_tool_20250718_maintenance_cleaner.py
```

### 2. 存放位置规范（强制）
- **临时文件** → `ai_temp/[子目录]/`
- **长期工具** → `ai_tools/[子目录]/`
- **分析报告** → `ai_reports/[子目录]/`
- **配置模板** → `ai_templates/[子目录]/`

### 3. 禁止行为
- ❌ 在项目根目录创建AI文件
- ❌ 使用不规范命名
- ❌ 创建无明确生命周期的文件

### 4. 必须行为
- ✅ 使用规范命名格式
- ✅ 放置到指定AI目录
- ✅ 添加文件生命周期说明
- ✅ 任务完成后处理临时文件

### 5. 对话结束处理
每次对话结束时必须：
1. 评估生成文件的长期价值
2. 提醒用户文件位置和生命周期
3. 建议使用管理工具：`python tools/ai_file_manager.py status`

### 6. 文件头部模板
每个AI生成文件必须包含：
```python
"""
AI生成文件信息
================
文件类型: [临时工具/长期工具/分析报告/配置模板]
生命周期: [7天/30天/永久保留]
创建目的: [具体用途说明]
清理条件: [何时可以安全删除]
"""
```

### 7. 管理工具集成
适时提醒用户使用AI文件管理工具：
```bash
python tools/ai_file_manager.py status    # 查看统计
python tools/ai_file_manager.py cleanup   # 清理过期文件
python tools/ai_file_manager.py init      # 初始化目录
```

### 8. 质量检查
创建文件前必须确认：
- [ ] 命名符合规范
- [ ] 目录位置正确
- [ ] 生命周期明确
- [ ] 用途必要且不重复

---
**重要**：这些规则在每次对话中都必须严格执行，确保项目文件结构的清洁和有序。
