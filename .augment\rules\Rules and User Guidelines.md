---
type: "always_apply"
description: "Example description"
---
### 代码质量标准
- 遵循 PEP 8，使用类型注解，行长度120字符
- SQLAlchemy模型必须明确声明字段类型和约束
- Streamlit组件使用缓存装饰器，表单提交后清理状态
- Loguru日志使用结构化格式，包含操作上下文

### 项目架构规范
```
src/qianchuan_aw/     # 核心业务代码
├── models/           # SQLAlchemy数据模型
├── sdk_qc/          # 千川API客户端
├── utils/           # 工具函数
└── workflows/       # 业务流程
tools/               # 独立工具脚本
config/              # 配置文件
```

### API设计模式
- 所有API方法必须有类型注解和文档字符串
- 使用统一的错误处理机制（QianchuanAPIError）
- 实现重试机制和熔断器模式
- API返回值统一处理，注意字典格式（如get_library_videos返回{"list": [...]}）

### 数据库规范
- 每个表必须有主键和时间戳字段
- 使用relationship()明确声明外键关系
- 敏感字段加密存储，查询使用索引优化
- 数据库操作使用事务管理确保一致性

### 错误处理标准
- 使用重试装饰器处理临时性错误
- 实现熔断器防止级联故障
- 结构化日志记录错误上下文
- 区分业务异常和系统异常
