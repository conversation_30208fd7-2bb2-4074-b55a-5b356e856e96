#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复收割工作流的触发条件和状态转换问题
清理条件: 功能被更好的解决方案替代时删除
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..'))

from loguru import logger
from typing import Dict, Any, List
import time
from datetime import datetime, timedelta

class HarvestWorkflowFixer:
    """收割工作流修复器"""
    
    def __init__(self):
        self.logger = logger
        
    def analyze_current_issues(self) -> Dict[str, Any]:
        """分析当前收割工作流的问题"""
        self.logger.info("🔍 分析收割工作流问题...")
        
        issues = {
            'trigger_condition_limited': {
                'description': 'handle_monitoring_of_materials只处理MONITORING状态的计划',
                'impact': 'AUDITING状态的计划不会被收割监控',
                'affected_plans': ['1839401235550651', '1839401296510571', '1839401377349770'],
                'severity': 'HIGH'
            },
            'status_transition_missing': {
                'description': '计划申诉后没有自动转换为MONITORING状态',
                'impact': '计划停留在AUDITING状态，不被持续监控',
                'severity': 'HIGH'
            },
            'material_status_sync_missing': {
                'description': '缺少从千川API同步素材审核状态的机制',
                'impact': '素材状态不能及时更新为approved',
                'severity': 'MEDIUM'
            },
            'test_account_limitation': {
                'description': '独立收割有测试账户限制',
                'impact': '非测试账户的计划不会被独立收割',
                'severity': 'MEDIUM'
            }
        }
        
        self.logger.info("📊 发现的问题:")
        for issue_key, issue_info in issues.items():
            self.logger.warning(f"❌ {issue_info['description']}")
            self.logger.warning(f"   影响: {issue_info['impact']}")
            self.logger.warning(f"   严重程度: {issue_info['severity']}")
        
        return issues
    
    def create_enhanced_monitoring_function(self) -> str:
        """创建增强的监控函数"""
        self.logger.info("🔧 创建增强的收割监控函数...")
        
        enhanced_function = '''
def handle_enhanced_monitoring_of_materials(db: Session, app_settings: Dict[str, Any]):
    """
    增强版素材收割监控
    支持AUDITING和MONITORING状态的计划
    """
    logger.info("--- [工作流3B-Enhanced] 开始：增强版持续收割并监控申诉状态 ---")
    try:
        # 扩展查询条件，包含AUDITING和MONITORING状态的计划
        plans_to_monitor = db.query(Campaign).options(
            joinedload(Campaign.account).joinedload(AdAccount.principal)
        ).filter(
            Campaign.status.in_(['MONITORING', 'AUDITING']),
            # 只监控最近24小时内的计划，避免处理过老的计划
            Campaign.created_at >= datetime.utcnow() - timedelta(hours=24)
        ).all()
        
        if not plans_to_monitor: 
            logger.info("没有需要监控的计划")
            return

        logger.info(f"找到 {len(plans_to_monitor)} 个需要监控的计划")
        
        plans_by_account = defaultdict(list)
        for plan in plans_to_monitor:
            plans_by_account[plan.account].append(plan)

        for account, plans_in_account in plans_by_account.items():
            principal = account.principal
            
            # 对每个计划执行收割检查
            for plan in plans_in_account:
                try:
                    # 1. 首先尝试收割素材
                    _harvest_materials_from_plan(db, plan, principal, app_settings)
                    
                    # 2. 如果计划状态为AUDITING且已申诉，转换为MONITORING
                    if plan.status == 'AUDITING' and plan.appeal_status in ['appeal_pending', 'appeal_submitted']:
                        logger.info(f"计划 {plan.campaign_id_qc} 已申诉，转换状态为MONITORING")
                        plan.status = 'MONITORING'
                        db.commit()
                        
                except Exception as e:
                    logger.error(f"处理计划 {plan.campaign_id_qc} 时发生错误: {e}")
                    continue

            # 3. 执行原有的申诉状态检查逻辑
            log_key = f"account_{account.id}_appeal_status_check"
            last_check_time = LOG_CACHE.get(log_key, 0)
            if time.time() - last_check_time < app_settings['robustness']['monitoring_interval']:
                logger.info(f"账户 {account.name} 未到监控时间，跳过申诉状态检查。")
                continue
            
            # 只对MONITORING状态的计划执行申诉状态检查
            monitoring_plans = [p for p in plans_in_account if p.status == 'MONITORING']
            if not monitoring_plans:
                continue
                
            logger.info(f"账户 {account.name} 开始批量查询申诉状态...")
            try:
                with CopilotSession(principal.name, account.account_id_qc, app_settings) as session:
                    for plan in monitoring_plans:
                        appeal_status = session.query_plan(plan.campaign_id_qc)
                        
                        if appeal_status in ["APPEAL_SUCCESS", "APPEAL_FAILED", "ALREADY_APPROVED"]:
                            if appeal_status == "APPEAL_SUCCESS":
                                logger.success(f"计划 {plan.campaign_id_qc} 申诉成功！进入最终收割。")
                            elif appeal_status == "ALREADY_APPROVED":
                                logger.success(f"计划 {plan.campaign_id_qc} 监控发现其已是"非拒绝状态"，视为通过，进入最终收割！")
                            else:
                                logger.warning(f"计划 {plan.campaign_id_qc} 申诉失败。进入最终收割。")
                            _harvest_and_complete_plan(db, plan, principal, app_settings)

                        elif appeal_status == "APPEAL_SUBMITTED":
                            logger.info(f"计划 {plan.campaign_id_qc} 申诉已提交，继续监控等待结果。")
                        
                        elif appeal_status == 'NO_RECORD':
                            # 业务铁律检查 - 防止重复提审
                            can_retry, reason = _can_safely_retry_appeal(plan)
                            if not can_retry:
                                logger.warning(f"计划 {plan.campaign_id_qc} 不能重试申诉: {reason}")
                                continue
                            
                            logger.info(f"计划 {plan.campaign_id_qc} 无申诉记录，可能需要重新申诉")

                LOG_CACHE[log_key] = time.time()
                
            except Exception as e:
                logger.error(f"为账户 {account.name} 执行申诉状态检查时发生错误: {e}", exc_info=True)

    except Exception as e:
        logger.error(f"[增强版收割监控] 发生严重错误: {e}", exc_info=True)
        db.rollback()
'''
        
        return enhanced_function
    
    def create_material_status_sync_function(self) -> str:
        """创建素材状态同步函数"""
        self.logger.info("🔄 创建素材状态同步函数...")
        
        sync_function = '''
def sync_material_audit_status(db: Session, app_settings: Dict[str, Any]):
    """
    同步素材审核状态
    从千川API获取最新的素材审核状态并更新本地状态
    """
    logger.info("--- [工作流3C] 开始：素材审核状态同步 ---")
    
    try:
        # 查询所有需要同步状态的计划
        active_campaigns = db.query(Campaign).options(
            joinedload(Campaign.account).joinedload(AdAccount.principal),
            joinedload(Campaign.platform_creatives).joinedload(PlatformCreative.local_creative)
        ).filter(
            Campaign.status.in_(['AUDITING', 'MONITORING']),
            Campaign.created_at >= datetime.utcnow() - timedelta(hours=48)  # 48小时内的计划
        ).all()
        
        if not active_campaigns:
            logger.info("没有需要同步状态的计划")
            return
        
        logger.info(f"找到 {len(active_campaigns)} 个需要同步状态的计划")
        
        campaigns_by_account = defaultdict(list)
        for campaign in active_campaigns:
            campaigns_by_account[campaign.account].append(campaign)
        
        total_synced = 0
        total_approved = 0
        
        for account, campaigns in campaigns_by_account.items():
            try:
                principal = account.principal
                client = QianchuanClient(
                    app_id=app_settings['api_credentials']['app_id'], 
                    secret=app_settings['api_credentials']['secret'], 
                    principal_id=principal.id
                )
                
                for campaign in campaigns:
                    try:
                        # 获取计划中的所有素材状态
                        materials_in_ad = client.get_materials_in_ad(
                            advertiser_id=int(account.account_id_qc),
                            ad_id=int(campaign.campaign_id_qc)
                        )
                        
                        if not materials_in_ad:
                            continue
                        
                        # 更新每个素材的状态
                        for material_info in materials_in_ad:
                            material_id = material_info.get("material_info", {}).get("video_material", {}).get("material_id")
                            if not material_id:
                                continue
                            
                            # 查找对应的平台素材记录
                            pc = db.query(PlatformCreative).filter(
                                PlatformCreative.material_id_qc == str(material_id)
                            ).first()
                            
                            if not pc or not pc.local_creative:
                                continue
                            
                            audit_status = material_info.get("audit_status")
                            current_status = pc.local_creative.status
                            
                            # 更新素材状态
                            if audit_status == 'PASS' and current_status != 'approved':
                                logger.info(f"素材 {material_id} 审核通过，更新状态为approved")
                                pc.local_creative.status = 'approved'
                                total_approved += 1
                                total_synced += 1
                                
                            elif audit_status in ['REJECT', 'AUDIT_REJECT'] and current_status != 'rejected':
                                logger.warning(f"素材 {material_id} 审核被拒，更新状态为rejected")
                                pc.local_creative.status = 'rejected'
                                total_synced += 1
                        
                        db.commit()
                        
                    except Exception as e:
                        logger.error(f"同步计划 {campaign.campaign_id_qc} 素材状态失败: {e}")
                        continue
                        
            except Exception as e:
                logger.error(f"为账户 {account.name} 同步素材状态失败: {e}")
                continue
        
        logger.success(f"素材状态同步完成: 共同步 {total_synced} 个素材，其中 {total_approved} 个审核通过")
        
    except Exception as e:
        logger.error(f"素材状态同步发生严重错误: {e}", exc_info=True)
        db.rollback()
'''
        
        return sync_function
    
    def generate_fix_recommendations(self) -> List[str]:
        """生成修复建议"""
        recommendations = [
            "1. 立即修复: 修改 handle_monitoring_of_materials 函数，包含 AUDITING 状态的计划",
            "2. 状态转换: 实现计划申诉后自动转换为 MONITORING 状态",
            "3. 素材同步: 添加定期从千川API同步素材审核状态的机制",
            "4. 增强监控: 实现不依赖计划状态的实时收割监控服务",
            "5. 错误处理: 增强收割失败的重试机制和错误日志",
            "6. 配置优化: 检查测试账户限制器配置，确保相关账户被包含",
            "7. 监控告警: 添加收割工作流执行状态的监控和告警机制"
        ]
        
        return recommendations
    
    def create_immediate_fix_script(self) -> str:
        """创建立即修复脚本"""
        self.logger.info("🚀 创建立即修复脚本...")
        
        fix_script = '''
def apply_immediate_harvest_fix():
    """立即修复收割工作流问题"""
    logger.info("🚀 开始应用收割工作流立即修复...")
    
    with SessionLocal() as db:
        try:
            # 1. 将状态为AUDITING且已申诉的计划转换为MONITORING
            auditing_plans = db.query(Campaign).filter(
                Campaign.status == 'AUDITING',
                Campaign.appeal_status.in_(['appeal_pending', 'appeal_submitted'])
            ).all()
            
            converted_count = 0
            for plan in auditing_plans:
                logger.info(f"转换计划 {plan.campaign_id_qc} 状态: AUDITING -> MONITORING")
                plan.status = 'MONITORING'
                converted_count += 1
            
            db.commit()
            logger.success(f"成功转换 {converted_count} 个计划状态")
            
            # 2. 手动触发一次收割工作流
            logger.info("手动触发收割工作流...")
            from qianchuan_aw.workflows.scheduler import handle_harvest
            from config.settings import load_app_settings
            
            app_settings = load_app_settings()
            success = handle_harvest(db, app_settings)
            
            if success:
                logger.success("收割工作流执行成功")
            else:
                logger.error("收割工作流执行失败")
            
            return True
            
        except Exception as e:
            logger.error(f"应用立即修复失败: {e}")
            db.rollback()
            return False

if __name__ == "__main__":
    apply_immediate_fix()
'''
        
        return fix_script

def main():
    """主函数"""
    fixer = HarvestWorkflowFixer()
    
    logger.info("🔧 千川收割工作流修复工具")
    logger.info("="*60)
    
    # 1. 分析问题
    issues = fixer.analyze_current_issues()
    
    # 2. 生成修复建议
    logger.info("\n💡 修复建议:")
    logger.info("-"*40)
    recommendations = fixer.generate_fix_recommendations()
    for rec in recommendations:
        logger.info(rec)
    
    # 3. 创建增强函数
    logger.info("\n🔧 生成增强版监控函数...")
    enhanced_function = fixer.create_enhanced_monitoring_function()
    
    logger.info("\n🔄 生成素材状态同步函数...")
    sync_function = fixer.create_material_status_sync_function()
    
    logger.info("\n🚀 生成立即修复脚本...")
    fix_script = fixer.create_immediate_fix_script()
    
    # 4. 保存修复代码
    with open('ai_temp/enhanced_harvest_monitoring.py', 'w', encoding='utf-8') as f:
        f.write(enhanced_function)
    
    with open('ai_temp/material_status_sync.py', 'w', encoding='utf-8') as f:
        f.write(sync_function)
    
    with open('ai_temp/immediate_harvest_fix.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    logger.success("✅ 修复工具执行完成")
    logger.info("📁 生成的文件:")
    logger.info("  - ai_temp/enhanced_harvest_monitoring.py")
    logger.info("  - ai_temp/material_status_sync.py") 
    logger.info("  - ai_temp/immediate_harvest_fix.py")

if __name__ == "__main__":
    main()
