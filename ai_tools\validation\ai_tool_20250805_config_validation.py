#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 验证工具
生命周期: 永久保留
创建目的: 验证配置修复是否正确，确保浏览器实例控制生效
清理条件: 成为配置管理组件后可归档
"""

import yaml
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.config_file = self.project_root / 'config' / 'settings.yml'
        self.validation_results = {
            'timestamp': datetime.now().isoformat(),
            'config_file_exists': False,
            'browser_config': {},
            'appeal_config': {},
            'workflow_config': {},
            'issues': [],
            'recommendations': [],
            'overall_status': 'UNKNOWN'
        }

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_file.exists():
            self.validation_results['issues'].append("配置文件不存在")
            return {}
        
        self.validation_results['config_file_exists'] = True
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            self.validation_results['issues'].append(f"配置文件加载失败: {e}")
            return {}

    def validate_browser_config(self, config: Dict) -> Dict:
        """验证浏览器配置"""
        print("🔍 验证浏览器配置...")
        
        browser_config = config.get('async_playwright', {})
        issues = []
        recommendations = []
        
        # 检查关键配置项
        max_sessions = browser_config.get('max_browser_sessions', None)
        session_timeout = browser_config.get('session_timeout', None)
        headless = browser_config.get('headless', None)
        enabled = browser_config.get('enabled', None)
        
        # 验证max_browser_sessions
        if max_sessions is None:
            issues.append("缺少 max_browser_sessions 配置")
        elif max_sessions > 2:
            issues.append(f"max_browser_sessions 过高: {max_sessions}，建议 ≤ 2")
        elif max_sessions == 1:
            print(f"  ✅ max_browser_sessions: {max_sessions} (优秀)")
        else:
            print(f"  ✅ max_browser_sessions: {max_sessions} (良好)")
        
        # 验证session_timeout
        if session_timeout is None:
            issues.append("缺少 session_timeout 配置")
        elif session_timeout > 300:
            issues.append(f"session_timeout 过长: {session_timeout}秒，建议 ≤ 300秒")
        else:
            print(f"  ✅ session_timeout: {session_timeout}秒 (合理)")
        
        # 验证headless
        if headless is None:
            recommendations.append("建议设置 headless: true 以节省资源")
        elif headless:
            print(f"  ✅ headless: {headless} (节省资源)")
        else:
            print(f"  ⚠️ headless: {headless} (消耗更多资源)")
        
        # 验证enabled
        if enabled is None:
            issues.append("缺少 enabled 配置")
        elif enabled:
            print(f"  ✅ enabled: {enabled}")
        else:
            print(f"  ⚠️ enabled: {enabled} (Playwright已禁用)")
        
        self.validation_results['browser_config'] = {
            'max_browser_sessions': max_sessions,
            'session_timeout': session_timeout,
            'headless': headless,
            'enabled': enabled,
            'issues': issues,
            'recommendations': recommendations
        }
        
        return browser_config

    def validate_appeal_config(self, config: Dict) -> Dict:
        """验证提审配置"""
        print("🔍 验证提审配置...")
        
        appeal_config = config.get('appeal_scheduler', {})
        issues = []
        recommendations = []
        
        # 检查关键配置项
        batch_size = appeal_config.get('batch_size_per_account', None)
        account_interval = appeal_config.get('account_interval_seconds', None)
        max_retry = appeal_config.get('max_retry_attempts', None)
        smart_retry = appeal_config.get('smart_retry_enabled', None)
        
        # 验证batch_size_per_account
        if batch_size is None:
            issues.append("缺少 batch_size_per_account 配置")
        elif batch_size > 5:
            issues.append(f"batch_size_per_account 过大: {batch_size}，建议 ≤ 5")
        elif batch_size <= 3:
            print(f"  ✅ batch_size_per_account: {batch_size} (优秀)")
        else:
            print(f"  ✅ batch_size_per_account: {batch_size} (良好)")
        
        # 验证account_interval_seconds
        if account_interval is None:
            issues.append("缺少 account_interval_seconds 配置")
        elif account_interval < 10:
            issues.append(f"account_interval_seconds 过短: {account_interval}秒，建议 ≥ 10秒")
        else:
            print(f"  ✅ account_interval_seconds: {account_interval}秒 (合理)")
        
        # 验证max_retry_attempts
        if max_retry is None:
            issues.append("缺少 max_retry_attempts 配置")
        elif max_retry > 1:
            issues.append(f"max_retry_attempts 过高: {max_retry}，违反'每个计划只提审一次'铁律")
        else:
            print(f"  ✅ max_retry_attempts: {max_retry} (符合业务铁律)")
        
        # 验证smart_retry_enabled
        if smart_retry is None:
            recommendations.append("建议设置 smart_retry_enabled: false")
        elif smart_retry:
            issues.append("smart_retry_enabled: true 可能导致重复提审")
        else:
            print(f"  ✅ smart_retry_enabled: {smart_retry} (防止重复提审)")
        
        self.validation_results['appeal_config'] = {
            'batch_size_per_account': batch_size,
            'account_interval_seconds': account_interval,
            'max_retry_attempts': max_retry,
            'smart_retry_enabled': smart_retry,
            'issues': issues,
            'recommendations': recommendations
        }
        
        return appeal_config

    def validate_workflow_config(self, config: Dict) -> Dict:
        """验证工作流配置"""
        print("🔍 验证工作流配置...")
        
        workflow_config = config.get('workflow', {})
        plan_appeal_config = workflow_config.get('plan_appeal', {})
        issues = []
        recommendations = []
        
        # 检查提审间隔
        appeal_interval = plan_appeal_config.get('interval_seconds', None)
        appeal_enabled = plan_appeal_config.get('enabled', None)
        
        # 验证interval_seconds
        if appeal_interval is None:
            issues.append("缺少 plan_appeal.interval_seconds 配置")
        elif appeal_interval < 600:
            issues.append(f"plan_appeal.interval_seconds 过短: {appeal_interval}秒，建议 ≥ 600秒")
        else:
            print(f"  ✅ plan_appeal.interval_seconds: {appeal_interval}秒 (合理)")
        
        # 验证enabled
        if appeal_enabled is None:
            issues.append("缺少 plan_appeal.enabled 配置")
        elif appeal_enabled:
            print(f"  ✅ plan_appeal.enabled: {appeal_enabled}")
        else:
            print(f"  ⚠️ plan_appeal.enabled: {appeal_enabled} (提审已禁用)")
        
        self.validation_results['workflow_config'] = {
            'plan_appeal_interval_seconds': appeal_interval,
            'plan_appeal_enabled': appeal_enabled,
            'issues': issues,
            'recommendations': recommendations
        }
        
        return workflow_config

    def generate_overall_assessment(self):
        """生成总体评估"""
        all_issues = []
        all_recommendations = []
        
        # 收集所有问题和建议
        for config_type in ['browser_config', 'appeal_config', 'workflow_config']:
            config_result = self.validation_results[config_type]
            all_issues.extend(config_result.get('issues', []))
            all_recommendations.extend(config_result.get('recommendations', []))
        
        self.validation_results['issues'] = all_issues
        self.validation_results['recommendations'] = all_recommendations
        
        # 确定总体状态
        if not all_issues:
            self.validation_results['overall_status'] = 'EXCELLENT'
        elif len(all_issues) <= 2:
            self.validation_results['overall_status'] = 'GOOD'
        elif len(all_issues) <= 5:
            self.validation_results['overall_status'] = 'NEEDS_IMPROVEMENT'
        else:
            self.validation_results['overall_status'] = 'CRITICAL'

    def run_validation(self):
        """运行完整验证"""
        print("🎯 千川自动化项目 - 配置验证工具")
        print("📌 目标: 验证浏览器实例控制配置是否正确")
        print("="*60)
        
        # 加载配置
        config = self.load_config()
        if not config:
            print("❌ 配置加载失败，无法继续验证")
            return self.validation_results
        
        print("✅ 配置文件加载成功")
        print()
        
        # 验证各个配置部分
        self.validate_browser_config(config)
        print()
        
        self.validate_appeal_config(config)
        print()
        
        self.validate_workflow_config(config)
        print()
        
        # 生成总体评估
        self.generate_overall_assessment()
        
        # 显示结果
        self.display_results()
        
        return self.validation_results

    def display_results(self):
        """显示验证结果"""
        print("="*60)
        print("📊 配置验证结果汇总")
        print("="*60)
        
        # 总体状态
        status_emoji = {
            'EXCELLENT': '🎉',
            'GOOD': '✅',
            'NEEDS_IMPROVEMENT': '⚠️',
            'CRITICAL': '❌'
        }
        
        status = self.validation_results['overall_status']
        print(f"{status_emoji.get(status, '❓')} 总体状态: {status}")
        
        # 问题列表
        if self.validation_results['issues']:
            print(f"\n❌ 发现 {len(self.validation_results['issues'])} 个问题:")
            for i, issue in enumerate(self.validation_results['issues'], 1):
                print(f"  {i}. {issue}")
        else:
            print(f"\n✅ 未发现配置问题")
        
        # 建议列表
        if self.validation_results['recommendations']:
            print(f"\n💡 {len(self.validation_results['recommendations'])} 条优化建议:")
            for i, rec in enumerate(self.validation_results['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        # 关键配置摘要
        print(f"\n📋 关键配置摘要:")
        browser_cfg = self.validation_results['browser_config']
        appeal_cfg = self.validation_results['appeal_config']
        workflow_cfg = self.validation_results['workflow_config']
        
        print(f"  🖥️ 最大浏览器会话: {browser_cfg.get('max_browser_sessions', 'N/A')}")
        print(f"  📦 批处理大小: {appeal_cfg.get('batch_size_per_account', 'N/A')}")
        print(f"  ⏱️ 账户间隔: {appeal_cfg.get('account_interval_seconds', 'N/A')}秒")
        print(f"  🔄 最大重试次数: {appeal_cfg.get('max_retry_attempts', 'N/A')}")
        print(f"  📅 提审间隔: {workflow_cfg.get('plan_appeal_interval_seconds', 'N/A')}秒")
        
        print(f"\n" + "="*60)
        
        if status == 'EXCELLENT':
            print("🎉 配置验证通过！浏览器实例控制应该生效")
        elif status == 'GOOD':
            print("✅ 配置基本正确，建议处理少量问题")
        elif status == 'NEEDS_IMPROVEMENT':
            print("⚠️ 配置需要改进，请处理发现的问题")
        else:
            print("❌ 配置存在严重问题，必须修复后才能生效")

def main():
    """主函数"""
    validator = ConfigValidator()
    results = validator.run_validation()
    
    # 保存验证报告
    report_path = validator.project_root / 'ai_reports' / 'validation' / f'config_validation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    report_path.parent.mkdir(parents=True, exist_ok=True)
    
    import json
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 验证报告已保存: {report_path}")

if __name__ == "__main__":
    main()
