"""
千川申诉自动化工具
================
功能: 基于Playwright录制的精确自动化申诉流程，用于捕获API参数
作者: AI Assistant
创建时间: 2025-07-28
版本: v1.0

主要功能:
1. 自动化执行完整的千川申诉流程
2. 捕获callback接口的完整参数
3. 提供API构造体所需的所有信息
4. 支持批量处理多个计划ID

使用场景:
- 获取真实的API调用参数
- 验证申诉流程的有效性
- 为API方式提审提供参数模板
"""

import json
import time
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger
from playwright.sync_api import sync_playwright, Page, BrowserContext

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright


class QianchuanAppealAutomation:
    """千川申诉自动化工具"""
    
    def __init__(self, principal_name: str = "缇萃百货"):
        self.principal_name = principal_name
        self.project_root = Path(__file__).parent.parent
        self.cookies_path = self.project_root / "config" / "browser_cookies.json"
        
        # 捕获的所有网络请求
        self.captured_requests = []
        self.callback_requests = []
        
    def _load_cookies(self) -> List[Dict[str, Any]]:
        """加载cookies"""
        try:
            if not self.cookies_path.exists():
                raise FileNotFoundError(f"Cookies文件不存在: {self.cookies_path}")
            
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if self.principal_name not in cookies_data:
                raise ValueError(f"未找到主体 '{self.principal_name}' 的cookies")
            
            cookies_array = cookies_data[self.principal_name].get("cookies", [])
            cleaned_cookies = clean_cookies_for_playwright(cookies_array)
            logger.info(f"✅ 加载了 {len(cleaned_cookies)} 个cookies")
            return cleaned_cookies
            
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            raise
    
    def _setup_comprehensive_capture(self, page: Page):
        """设置全面的网络请求捕获"""
        def handle_request(request):
            """处理网络请求"""
            try:
                url = request.url
                method = request.method
                
                # 捕获所有POST请求到copilot相关接口
                if 'copilot' in url and method == 'POST':
                    logger.info(f"🎯 捕获到copilot POST请求: {url}")
                    
                    try:
                        # 尝试多种方式获取POST数据
                        post_data = None
                        try:
                            post_data = request.post_data
                            if post_data:
                                logger.info(f"✅ 成功获取POST数据，大小: {len(post_data)} 字符")
                        except Exception as e:
                            logger.warning(f"获取post_data失败: {e}")
                            # 尝试其他方式
                            try:
                                post_data = request.post_data_buffer
                                if post_data:
                                    logger.info(f"✅ 通过post_data_buffer获取数据")
                            except Exception as e2:
                                logger.warning(f"post_data_buffer也失败: {e2}")

                        headers = dict(request.headers)

                        request_info = {
                            'url': url,
                            'method': method,
                            'headers': headers,
                            'timestamp': time.time(),
                            'is_callback': 'callback' in url,
                            'debug_info': f"request_type: {type(request)}"
                        }

                        if post_data:
                            try:
                                data = json.loads(post_data)
                                request_info['data'] = data
                                request_info['raw_data'] = post_data

                                # 如果是callback请求，记录关键信息
                                if 'callback' in url:
                                    logger.success(f"🎯 成功捕获callback POST数据!")
                                    if 'sessionId' in data:
                                        logger.info(f"📋 sessionId: {data.get('sessionId')}")
                                    if 'callBackCode' in data:
                                        logger.info(f"📋 callBackCode: {data.get('callBackCode')}")
                                    if 'callValue' in data:
                                        logger.info(f"📋 callValue长度: {len(str(data.get('callValue', '')))}")

                            except json.JSONDecodeError as e:
                                request_info['raw_data'] = post_data
                                request_info['data'] = None
                                logger.warning(f"POST数据不是有效JSON: {e}")
                                logger.info(f"原始数据前100字符: {post_data[:100]}...")
                        else:
                            request_info['data'] = None
                            request_info['raw_data'] = None
                            if 'callback' in url:
                                logger.warning(f"⚠️ callback请求没有POST数据 - 这可能是问题所在!")
                        
                        self.captured_requests.append(request_info)
                        
                        if 'callback' in url:
                            self.callback_requests.append(request_info)
                            logger.success(f"🎉 捕获到callback请求！")
                            
                            if request_info.get('data'):
                                data = request_info['data']
                                logger.info(f"📋 关键参数:")
                                logger.info(f"  sessionId: {data.get('sessionId', 'N/A')}")
                                logger.info(f"  windowId: {data.get('windowId', 'N/A')[:20]}...")
                                logger.info(f"  messageId: {data.get('messageId', 'N/A')}")
                                logger.info(f"  callBackCode: {data.get('callBackCode', 'N/A')}")
                                
                    except Exception as e:
                        logger.warning(f"处理POST数据失败: {e}")
                        
            except Exception as e:
                logger.debug(f"处理请求失败: {e}")
        
        def handle_response(response):
            """处理网络响应"""
            try:
                url = response.url
                if 'copilot' in url and response.request.method == 'POST':
                    logger.info(f"📥 收到copilot响应: {response.status}")
                    
                    # 查找对应的请求
                    for req in self.captured_requests:
                        if req['url'] == url and abs(req['timestamp'] - time.time()) < 10:
                            try:
                                response_text = response.text()
                                req['response'] = {
                                    'status': response.status,
                                    'headers': dict(response.headers),
                                    'text': response_text
                                }
                                
                                try:
                                    req['response']['json'] = json.loads(response_text)
                                except:
                                    pass
                                    
                            except Exception as e:
                                logger.debug(f"获取响应内容失败: {e}")
                            break
                            
            except Exception as e:
                logger.debug(f"处理响应失败: {e}")
        
        page.on("request", handle_request)
        page.on("response", handle_response)
    
    def execute_appeal_automation(self, advertiser_id: str, plan_id: str, keep_open: bool = False) -> Dict[str, Any]:
        """执行自动化申诉流程并捕获完整参数
        keep_open: 是否在结束后暂时保留浏览器窗口（仅用于人工观察/调试）
        """
        playwright = None
        browser = None
        context = None
        
        try:
            logger.info(f"🚀 开始执行千川申诉自动化流程...")
            logger.info(f"📋 广告户ID: {advertiser_id}")
            logger.info(f"📋 计划ID: {plan_id}")
            
            # 启动浏览器
            cookies = self._load_cookies()
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(headless=False)
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            context.add_cookies(cookies)
            page = context.new_page()
            page.set_default_timeout(30000)
            
            # 设置全面的网络捕获
            self._setup_comprehensive_capture(page)
            
            # 导航到千川后台
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}"
            logger.info(f"📍 导航到千川后台: {url}")
            page.goto(url)
            page.wait_for_timeout(3000)
            logger.info("✅ 页面加载完成")
            
            # 执行完整的申诉流程
            success = self._execute_complete_flow(page, plan_id)
            
            if not success:
                return {"success": False, "error": "申诉流程执行失败"}
            
            # 等待所有网络请求完成
            logger.info("⏳ 等待网络请求完成...")
            page.wait_for_timeout(5000)
            
            # 分析捕获的参数
            result = self._analyze_captured_data()
            
            # 保存完整的捕获数据
            self._save_complete_capture_data(advertiser_id, plan_id, result)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 自动化流程执行失败: {e}")
            return {"success": False, "error": str(e)}
            
        finally:
            # 统一的资源清理逻辑
            try:
                # 是否保留窗口给人眼观察
                import os
                keep_env = os.environ.get("QC_KEEP_BROWSER_OPEN", "0")
                if keep_open or keep_env == "1":
                    logger.info("🔍 调试模式：保持浏览器打开 10 秒以便观察...")
                    time.sleep(10)
            except Exception:
                pass
            finally:
                try:
                    if context:
                        context.close()
                    if browser:
                        browser.close()
                    if playwright:
                        playwright.stop()
                except Exception as e_cleanup:
                    logger.warning(f"清理浏览器资源时出现问题: {e_cleanup}")
                    if playwright:
                        playwright.stop()
                except:
                    pass
    
    def _execute_complete_flow(self, page: Page, plan_id: str) -> bool:
        """执行完整的申诉流程（基于录制的精确选择器）"""
        try:
            logger.info("🎯 开始执行完整申诉流程...")
            
            # 步骤1: 点击智投星图标
            logger.info("🔍 步骤1: 点击智投星图标...")
            copilot_element = page.locator("#copilot-sdk-ark div").nth(3)
            copilot_element.click(timeout=10000)
            logger.info("✅ 智投星图标点击成功")
            page.wait_for_timeout(3000)
            
            # 步骤2: 输入文字指令
            logger.info("🔍 步骤2: 输入文字指令...")
            input_element = page.get_by_placeholder("请描述你的问题")
            input_element.click()
            input_element.fill("自助申诉表单")
            input_element.press("Enter")
            logger.info("✅ 文字指令发送成功")
            page.wait_for_timeout(5000)
            
            # 步骤3: 点击"计划/商品申诉"
            logger.info("🔍 步骤3: 点击'计划/商品申诉'...")
            plan_appeal_element = page.get_by_text("计划/商品申诉").first
            plan_appeal_element.click(timeout=10000)
            logger.info("✅ 计划/商品申诉点击成功")
            page.wait_for_timeout(3000)
            
            # 步骤4: 选择问题类型（保持一致性，都使用last）
            logger.info("🔍 步骤4: 选择问题类型...")
            dropdown_element = page.get_by_placeholder("请选择", exact=True).last
            dropdown_element.click(timeout=5000)
            page.wait_for_timeout(1000)
            
            option_element = page.get_by_text("计划审核不通过/结果申诉").last
            option_element.click(timeout=10000)
            logger.info("✅ 问题类型选择成功")
            page.wait_for_timeout(2000)
            
            # 步骤5: 输入计划ID（保持一致性，使用last）
            logger.info("🔍 步骤5: 输入计划ID...")
            plan_id_element = page.get_by_placeholder("请输入计划ID，支持托管计划").last
            plan_id_element.click()
            plan_id_element.fill(plan_id)
            logger.info(f"✅ 计划ID输入成功: {plan_id}")
            page.wait_for_timeout(1000)
            
            # 步骤6: 点击提交按钮（保持一致性，使用last）
            logger.info("🔍 步骤6: 点击提交按钮...")
            submit_element = page.get_by_role("button", name="提交").last
            submit_element.click(timeout=10000)
            logger.info("✅ 提交按钮点击成功")
            page.wait_for_timeout(3000)
            
            logger.success("🎉 完整申诉流程执行成功！")
            return True
            
        except Exception as e:
            logger.error(f"执行申诉流程失败: {e}")
            return False
    
    def _analyze_captured_data(self) -> Dict[str, Any]:
        """分析捕获的数据"""
        try:
            logger.info("🔍 分析捕获的网络请求数据...")
            
            result = {
                "success": True,
                "total_requests": len(self.captured_requests),
                "callback_requests": len(self.callback_requests),
                "captured_at": time.time(),
                "requests": self.captured_requests,
                "callbacks": self.callback_requests
            }
            
            if self.callback_requests:
                # 提取最重要的callback参数
                latest_callback = self.callback_requests[-1]

                # 构建API参数模板
                api_params = {
                    "url": latest_callback['url'],
                    "method": "POST",
                    "headers": latest_callback.get('headers', {}),
                    "query_params": {
                        "appCode": "QC",
                        "aavid": latest_callback['url'].split('aavid=')[1] if 'aavid=' in latest_callback['url'] else ""
                    }
                }

                # 如果有POST数据，添加到模板中
                if latest_callback.get('data'):
                    api_params["post_data"] = latest_callback['data']
                elif latest_callback.get('raw_data'):
                    api_params["raw_post_data"] = latest_callback['raw_data']

                result.update({"api_params": api_params})
                
                logger.success(f"✅ 成功分析 {len(self.callback_requests)} 个callback请求")
            else:
                logger.warning("⚠️ 未捕获到callback请求")
                result["success"] = False
                result["error"] = "未捕获到callback请求"
            
            return result
            
        except Exception as e:
            logger.error(f"分析捕获数据失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _save_complete_capture_data(self, advertiser_id: str, plan_id: str, result: Dict[str, Any]):
        """保存完整的捕获数据"""
        try:
            timestamp = int(time.time())
            filename = f"tools/captured_data/appeal_automation_{advertiser_id}_{plan_id}_{timestamp}.json"
            
            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            
            # 保存详细数据
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"💾 完整捕获数据已保存到: {filename}")
            
            # 同时保存API参数模板
            if result.get('api_params'):
                api_template_file = f"tools/captured_data/api_template_{timestamp}.json"
                with open(api_template_file, 'w', encoding='utf-8') as f:
                    json.dump(result['api_params'], f, ensure_ascii=False, indent=2, default=str)
                logger.info(f"💾 API参数模板已保存到: {api_template_file}")
            
        except Exception as e:
            logger.error(f"保存捕获数据失败: {e}")


def execute_qianchuan_appeal_automation(
    advertiser_id: str, 
    plan_id: str, 
    principal_name: str = "缇萃百货"
) -> Dict[str, Any]:
    """
    便捷函数：执行千川申诉自动化并捕获API参数
    
    Args:
        advertiser_id: 广告户ID
        plan_id: 计划ID
        principal_name: 主体名称
        
    Returns:
        包含完整API参数的结果字典
    """
    automation = QianchuanAppealAutomation(principal_name)
    return automation.execute_appeal_automation(advertiser_id, plan_id)


if __name__ == "__main__":
    # 测试自动化流程
    test_advertiser_id = "1836333804939273"
    test_plan_id = "1838840072680523"
    
    print("🎯 千川申诉自动化工具")
    print("=" * 50)
    print("功能：")
    print("1. 自动化执行完整申诉流程")
    print("2. 捕获所有网络请求参数")
    print("3. 提供API构造体模板")
    print("4. 保存详细的捕获数据")
    print()
    
    try:
        result = execute_qianchuan_appeal_automation(test_advertiser_id, test_plan_id)
        
        if result.get('success'):
            print("🎉 自动化流程执行成功！")
            print(f"📊 捕获统计:")
            print(f"  总请求数: {result.get('total_requests', 0)}")
            print(f"  callback请求数: {result.get('callback_requests', 0)}")
            
            if result.get('api_params'):
                print("✅ API参数已成功捕获并保存")
            else:
                print("⚠️ 未捕获到API参数")
        else:
            print(f"❌ 自动化流程失败: {result.get('error')}")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
    
    print("\n💡 使用方法:")
    print("from tools.qianchuan_appeal_automation import execute_qianchuan_appeal_automation")
    print("result = execute_qianchuan_appeal_automation('广告户ID', '计划ID')")
