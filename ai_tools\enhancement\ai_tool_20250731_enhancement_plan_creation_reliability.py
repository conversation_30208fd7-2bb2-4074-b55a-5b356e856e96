"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 增强计划创建可靠性，实现铁律2改进方案
清理条件: 当计划创建可靠性机制被更好的方案替代时可删除
"""

import os
import sys
import time
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger


class PlanCreationErrorType(Enum):
    """计划创建错误类型分类"""
    TEMPORARY_API = "temporary_api"          # 临时API问题
    TEMPORARY_NETWORK = "temporary_network"  # 临时网络问题
    TEMPORARY_SERVER = "temporary_server"    # 临时服务器问题
    PERMANENT_CONFIG = "permanent_config"    # 配置错误
    PERMANENT_QUOTA = "permanent_quota"      # 配额问题
    PERMANENT_ACCOUNT = "permanent_account"  # 账户问题
    UNKNOWN = "unknown"                      # 未知错误


class PlanCreationReliabilityEnhancer:
    """计划创建可靠性增强器"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        self.max_retries = 3  # 计划创建重试次数
        self.base_delay = 30  # 基础延迟30秒
        
    def classify_creation_error(self, error_message: str, status_code: Optional[int] = None) -> PlanCreationErrorType:
        """分类计划创建错误类型"""
        error_msg = str(error_message).lower()
        
        # 临时性API错误
        api_keywords = [
            'rate limit', 'limit exceeded', 'too many requests',
            'quota exceeded', 'throttled', 'api limit'
        ]
        
        # 临时性网络错误
        network_keywords = [
            'timeout', 'connection', 'network', 'dns', 'socket',
            'connection reset', 'connection refused'
        ]
        
        # 临时性服务器错误
        server_keywords = [
            'server error', 'internal server error', 'service unavailable',
            'temporarily unavailable', 'maintenance', 'overloaded'
        ]
        
        # 永久性配置错误
        config_keywords = [
            'invalid parameter', 'missing parameter', 'invalid config',
            'validation failed', 'invalid format', 'bad request'
        ]
        
        # 永久性配额错误
        quota_keywords = [
            'quota exhausted', 'budget insufficient', 'account suspended',
            'plan limit reached', 'daily limit exceeded'
        ]
        
        # 永久性账户错误
        account_keywords = [
            'account blocked', 'account disabled', 'unauthorized',
            'access denied', 'permission denied', 'account error'
        ]
        
        # 根据状态码判断
        if status_code:
            if status_code in [429]:  # Too Many Requests
                return PlanCreationErrorType.TEMPORARY_API
            elif status_code in [500, 502, 503, 504]:  # Server errors
                return PlanCreationErrorType.TEMPORARY_SERVER
            elif status_code in [400, 422]:  # Bad request, validation error
                return PlanCreationErrorType.PERMANENT_CONFIG
            elif status_code in [401, 403]:  # Auth errors
                return PlanCreationErrorType.PERMANENT_ACCOUNT
        
        # 根据错误消息判断
        if any(keyword in error_msg for keyword in api_keywords):
            return PlanCreationErrorType.TEMPORARY_API
        elif any(keyword in error_msg for keyword in network_keywords):
            return PlanCreationErrorType.TEMPORARY_NETWORK
        elif any(keyword in error_msg for keyword in server_keywords):
            return PlanCreationErrorType.TEMPORARY_SERVER
        elif any(keyword in error_msg for keyword in config_keywords):
            return PlanCreationErrorType.PERMANENT_CONFIG
        elif any(keyword in error_msg for keyword in quota_keywords):
            return PlanCreationErrorType.PERMANENT_QUOTA
        elif any(keyword in error_msg for keyword in account_keywords):
            return PlanCreationErrorType.PERMANENT_ACCOUNT
        
        return PlanCreationErrorType.UNKNOWN
    
    def should_retry_creation(self, attempt: int, error_type: PlanCreationErrorType) -> bool:
        """判断是否应该重试计划创建"""
        if attempt >= self.max_retries:
            return False
        
        # 永久性错误不重试
        if error_type in [PlanCreationErrorType.PERMANENT_CONFIG, 
                         PlanCreationErrorType.PERMANENT_QUOTA, 
                         PlanCreationErrorType.PERMANENT_ACCOUNT]:
            return False
        
        return True
    
    def calculate_creation_retry_delay(self, attempt: int, error_type: PlanCreationErrorType) -> int:
        """计算计划创建重试延迟时间"""
        if error_type in [PlanCreationErrorType.PERMANENT_CONFIG, 
                         PlanCreationErrorType.PERMANENT_QUOTA, 
                         PlanCreationErrorType.PERMANENT_ACCOUNT]:
            return 0  # 永久性错误不重试
        
        # 线性增长延迟
        delay = self.base_delay * (attempt + 1)
        
        # 根据错误类型调整延迟
        if error_type == PlanCreationErrorType.TEMPORARY_API:
            delay *= 2  # API错误延迟更长
        elif error_type == PlanCreationErrorType.TEMPORARY_NETWORK:
            delay *= 0.5  # 网络错误延迟较短
        
        return min(int(delay), 300)  # 最大5分钟
    
    def verify_plan_creation_success(self, client, account_id: int, plan_id: str, plan_name: str) -> Dict[str, Any]:
        """验证计划创建是否真正成功"""
        try:
            logger.info(f"🔍 验证计划创建结果: plan_id={plan_id}")
            
            # 方法1: 通过计划ID查询计划详情
            plan_details = client.get_ad_plan_detail(advertiser_id=account_id, ad_id=plan_id)
            
            if plan_details and plan_details.get('ad_id') == plan_id:
                plan_status = plan_details.get('status', '')
                plan_audit_status = plan_details.get('audit_status', '')
                
                # 检查计划状态
                if plan_status in ['ENABLE', 'DISABLE'] and plan_audit_status in ['AUDITING', 'APPROVED']:
                    logger.success(f"✅ 计划创建验证成功: plan_id={plan_id}")
                    logger.info(f"   计划状态: {plan_status}")
                    logger.info(f"   审核状态: {plan_audit_status}")
                    logger.info(f"   计划名称: {plan_details.get('name', plan_name)}")
                    
                    return {
                        'verified': True,
                        'plan_status': plan_status,
                        'audit_status': plan_audit_status,
                        'plan_name': plan_details.get('name', plan_name),
                        'error': None
                    }
                else:
                    logger.warning(f"⚠️ 计划状态异常: plan_id={plan_id}")
                    logger.warning(f"   计划状态: {plan_status}")
                    logger.warning(f"   审核状态: {plan_audit_status}")
                    
                    return {
                        'verified': False,
                        'plan_status': plan_status,
                        'audit_status': plan_audit_status,
                        'plan_name': plan_details.get('name', plan_name),
                        'error': f"计划状态异常: status={plan_status}, audit_status={plan_audit_status}"
                    }
            
            # 方法2: 通过计划列表查询
            logger.info("尝试通过计划列表查询验证...")
            plan_list = client.get_ad_plan_list(advertiser_id=account_id, filtering={'ad_ids': [plan_id]})
            
            if plan_list and plan_list.get('list'):
                plans = plan_list['list']
                for plan in plans:
                    if str(plan.get('ad_id')) == str(plan_id):
                        logger.success(f"✅ 通过计划列表验证成功: plan_id={plan_id}")
                        return {
                            'verified': True,
                            'plan_status': plan.get('status', ''),
                            'audit_status': plan.get('audit_status', ''),
                            'plan_name': plan.get('name', plan_name),
                            'error': None
                        }
            
            logger.warning(f"⚠️ 计划创建验证失败: 未找到 plan_id={plan_id}")
            return {
                'verified': False,
                'plan_status': None,
                'audit_status': None,
                'plan_name': plan_name,
                'error': f"未找到计划: plan_id={plan_id}"
            }
            
        except Exception as e:
            logger.error(f"计划创建验证过程中出错: {e}")
            return {
                'verified': False,
                'plan_status': None,
                'audit_status': None,
                'plan_name': plan_name,
                'error': f"验证过程出错: {str(e)}"
            }
    
    def send_plan_creation_failure_alert(self, account_name: str, plan_name: str, error_message: str, 
                                       error_type: PlanCreationErrorType, attempt_count: int, 
                                       platform_creatives: List = None):
        """发送计划创建失败告警"""
        try:
            material_count = len(platform_creatives) if platform_creatives else 0
            material_info = []
            
            if platform_creatives:
                for pc in platform_creatives[:3]:  # 只显示前3个素材
                    if hasattr(pc, 'local_creative') and pc.local_creative:
                        filename = os.path.basename(pc.local_creative.file_path) if pc.local_creative.file_path else "未知文件"
                        material_info.append(filename)
            
            alert_data = {
                'timestamp': datetime.now().isoformat(),
                'alert_type': 'plan_creation_failure',
                'severity': 'high' if error_type.value.startswith('permanent') else 'medium',
                'account_name': account_name,
                'plan_name': plan_name,
                'error_type': error_type.value,
                'error_message': error_message,
                'attempt_count': attempt_count,
                'max_retries': self.max_retries,
                'material_count': material_count,
                'material_samples': material_info[:3]
            }
            
            # 记录到日志
            logger.error(f"🚨 计划创建失败告警: {plan_name}")
            logger.error(f"   账户: {account_name}")
            logger.error(f"   错误类型: {error_type.value}")
            logger.error(f"   错误信息: {error_message}")
            logger.error(f"   重试次数: {attempt_count}/{self.max_retries}")
            logger.error(f"   素材数量: {material_count}")
            
            # TODO: 集成实际的告警系统
            self._write_alert_to_file(alert_data)
            
        except Exception as e:
            logger.error(f"发送计划创建失败告警时出错: {e}")
    
    def _write_alert_to_file(self, alert_data: Dict[str, Any]):
        """将告警写入文件（临时方案）"""
        try:
            alert_dir = Path("ai_reports/alerts")
            alert_dir.mkdir(parents=True, exist_ok=True)
            
            alert_file = alert_dir / f"plan_creation_failures_{datetime.now().strftime('%Y%m%d')}.log"
            
            with open(alert_file, 'a', encoding='utf-8') as f:
                import json
                f.write(json.dumps(alert_data, ensure_ascii=False) + '\n')
                
        except Exception as e:
            logger.error(f"写入告警文件失败: {e}")
    
    def get_plan_creation_statistics(self) -> Dict[str, Any]:
        """获取计划创建统计信息"""
        try:
            from qianchuan_aw.database.connection import database_session
            from qianchuan_aw.database.models import Campaign
            from sqlalchemy import func
            
            with database_session() as db:
                # 统计各种状态的计划数量
                stats = db.query(
                    Campaign.status,
                    func.count(Campaign.id).label('count')
                ).group_by(Campaign.status).all()
                
                status_counts = {stat.status: stat.count for stat in stats}
                
                # 计算创建成功率
                total_plans = sum(status_counts.values())
                successful_plans = sum(count for status, count in status_counts.items() 
                                     if status in ['AUDITING', 'APPROVED', 'DELIVERING', 'PAUSED'])
                
                success_rate = (successful_plans / total_plans * 100) if total_plans > 0 else 0
                
                return {
                    'total_plans': total_plans,
                    'successful_plans': successful_plans,
                    'plan_creation_success_rate': round(success_rate, 2),
                    'status_breakdown': status_counts,
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取计划创建统计信息失败: {e}")
            return {}


def main():
    """测试计划创建可靠性增强器"""
    logger.info("🔧 测试计划创建可靠性增强器")
    
    # 模拟配置
    app_settings = {
        'workflow': {
            'plan_creation': {
                'max_retries': 3,
                'retry_delay': 30
            }
        }
    }
    
    enhancer = PlanCreationReliabilityEnhancer(app_settings)
    
    # 测试错误分类
    test_errors = [
        ("Rate limit exceeded", 429),
        ("Invalid parameter: budget", 400),
        ("Account blocked", 403),
        ("Server internal error", 500),
        ("Connection timeout", None),
    ]
    
    for error_msg, status_code in test_errors:
        error_type = enhancer.classify_creation_error(error_msg, status_code)
        should_retry = enhancer.should_retry_creation(1, error_type)
        delay = enhancer.calculate_creation_retry_delay(1, error_type)
        
        logger.info(f"错误: {error_msg}")
        logger.info(f"  类型: {error_type.value}")
        logger.info(f"  重试: {should_retry}")
        logger.info(f"  延迟: {delay}秒")
        logger.info("---")
    
    # 获取统计信息
    stats = enhancer.get_plan_creation_statistics()
    if stats:
        logger.info("📊 计划创建统计信息:")
        logger.info(f"  总计划数: {stats.get('total_plans', 0)}")
        logger.info(f"  成功计划: {stats.get('successful_plans', 0)}")
        logger.info(f"  成功率: {stats.get('plan_creation_success_rate', 0)}%")


if __name__ == "__main__":
    main()
