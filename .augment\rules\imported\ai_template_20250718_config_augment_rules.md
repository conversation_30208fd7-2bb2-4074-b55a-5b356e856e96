---
type: "always_apply"
---

# AI文件管理规则 - Augment Rules

## 核心原则
在千川自动化项目中，AI助手必须严格遵循以下文件管理规则，防止临时文件污染项目结构。

## 1. 文件创建规则

### 命名规范（强制执行）
所有AI生成的文件必须使用以下命名格式：
```
ai_[类型]_[日期]_[用途]_[描述].[扩展名]
```

**类型前缀**：
- `ai_temp_`: 临时使用文件（7天生命周期）
- `ai_tool_`: 长期保留工具（永久保留）
- `ai_report_`: 分析报告文档（30天生命周期）
- `ai_template_`: 配置模板文件（永久保留）
- `ai_debug_`: 调试脚本（临时文件）
- `ai_analysis_`: 分析脚本（临时文件）

**日期格式**：使用 `YYYYMMDD` 格式

**示例**：
```
ai_temp_20250718_cleanup_docs_directory.py
ai_report_20250718_audit_deep_file_analysis.md
ai_tool_20250718_maintenance_file_manager.py
ai_template_20250718_config_deployment_settings.yml
```

### 存放位置规则（强制执行）
AI生成的文件必须存放在指定目录：

- **临时文件** → `ai_temp/[子目录]/`
  - `ai_temp/cleanup/`: 清理脚本
  - `ai_temp/debug/`: 调试脚本
  - `ai_temp/analysis/`: 分析脚本
  - `ai_temp/experiment/`: 实验脚本

- **长期工具** → `ai_tools/[子目录]/`
  - `ai_tools/maintenance/`: 维护工具
  - `ai_tools/analysis/`: 分析工具
  - `ai_tools/cleanup/`: 清理工具
  - `ai_tools/migration/`: 迁移工具
  - `ai_tools/optimization/`: 优化工具

- **报告文档** → `ai_reports/[子目录]/`
  - `ai_reports/audit/`: 审计报告
  - `ai_reports/analysis/`: 分析报告
  - `ai_reports/security/`: 安全报告
  - `ai_reports/performance/`: 性能报告

- **配置模板** → `ai_templates/[子目录]/`
  - `ai_templates/config/`: 配置模板
  - `ai_templates/scripts/`: 脚本模板
  - `ai_templates/deploy/`: 部署模板

## 2. 文件生命周期管理

### 自动分类规则
AI助手在创建文件时必须：
1. 评估文件的长期价值
2. 选择合适的类型前缀
3. 放置到正确的目录
4. 在文件头部添加生命周期说明

### 生命周期标识
在每个AI生成文件的头部必须包含：
```python
"""
AI生成文件信息
================
文件类型: [临时工具/长期工具/分析报告/配置模板]
生命周期: [7天/30天/永久保留]
创建目的: [具体用途说明]
依赖关系: [是否被其他文件引用]
清理条件: [何时可以安全删除]
"""
```

## 3. 禁止行为

### 严格禁止
- ❌ 在项目根目录直接创建AI文件
- ❌ 使用不规范的文件命名
- ❌ 创建没有明确生命周期的文件
- ❌ 在核心业务目录中创建临时文件
- ❌ 创建重复功能的工具脚本

### 必须执行
- ✅ 使用规范的命名格式
- ✅ 放置到指定的AI目录
- ✅ 添加文件头部信息
- ✅ 评估文件的长期价值
- ✅ 任务完成后处理临时文件

## 4. 任务完成后处理

### 处理流程
每次对话任务完成后，AI助手必须：

1. **评估生成的文件**：
   - 是否解决了长期问题？→ 移至 `ai_tools/`
   - 是否包含重要分析？→ 移至 `ai_reports/`
   - 是否仅为临时调试？→ 保留在 `ai_temp/`
   - 是否为配置模板？→ 移至 `ai_templates/`

2. **提醒用户**：
   ```
   📋 本次对话生成了以下AI文件：
   - ai_temp_20250718_cleanup_script.py (临时清理脚本，7天后自动删除)
   - ai_report_20250718_analysis_report.md (分析报告，30天后清理)
   
   💡 建议：运行 'python tools/ai_file_manager.py status' 查看AI文件统计
   ```

3. **清理建议**：
   - 对于一次性使用的脚本，建议立即删除
   - 对于有价值的工具，建议移至永久目录
   - 对于重要报告，建议移至doc/目录长期保存

## 5. 集成工具使用

### 必须使用的管理命令
AI助手在适当时机应该提醒用户使用：

```bash
# 查看AI文件统计
python tools/ai_file_manager.py status

# 清理过期文件
python tools/ai_file_manager.py cleanup

# 预览清理效果
python tools/ai_file_manager.py cleanup --dry-run
```

### 自动化集成
确保项目配置文件 `config/settings.yml` 中的AI文件管理配置生效：
- 自动清理功能已启用
- 生命周期规则正确配置
- 通知机制正常工作

## 6. 质量保证

### 文件创建检查清单
每次创建AI文件时必须确认：
- [ ] 文件名符合命名规范
- [ ] 文件放置在正确目录
- [ ] 文件头部包含生命周期信息
- [ ] 文件用途明确且必要
- [ ] 不与现有文件重复

### 对话结束检查清单
每次对话结束时必须确认：
- [ ] 所有AI生成文件已正确分类
- [ ] 临时文件已标记生命周期
- [ ] 重要文件已提醒用户保存
- [ ] 用户了解文件管理命令
- [ ] 项目结构保持清洁

## 7. 异常处理

### 文件冲突处理
如果发现文件名冲突：
1. 在文件名后添加时间戳：`_HHMM`
2. 检查是否为重复功能
3. 如果是重复功能，删除旧文件或合并功能

### 目录不存在处理
如果AI目录结构不存在：
1. 自动运行：`python tools/ai_file_manager.py init`
2. 创建必要的目录结构
3. 继续按规范创建文件

## 8. 监控和报告

### 定期提醒
AI助手应该在适当时机提醒：
- AI文件数量统计
- 存储空间使用情况
- 过期文件清理建议
- 重要文件归档建议

### 统计报告
定期生成AI文件管理统计：
- 各类型文件数量
- 存储空间占用
- 清理效果统计
- 用户使用习惯分析

---

**重要提醒**：这些规则是强制性的，AI助手在每次对话中都必须严格遵循，确保千川自动化项目的文件结构始终保持清洁和有序。
