"""
智能提审服务增强工具
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 实现智能提审重试机制，包含备用浏览器提审方案
清理条件: 成为核心功能后可集成到主代码
"""

import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy.orm import Session
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import Campaign
from ai_tool_20250801_enhancement_plan_status_checker import create_plan_status_checker


class SmartAppealService:
    """智能提审服务"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        self.status_checker = create_plan_status_checker(app_settings)
        self.max_retries = 3
        self.retry_delays = [30, 60, 120]  # 重试延迟：30秒、1分钟、2分钟
        
    def smart_appeal_with_retry(self, db: Session, campaign: Campaign) -> <PERSON><PERSON>[bool, str, Dict]:
        """
        智能提审，支持重试和备用方案
        
        Args:
            db: 数据库会话
            campaign: 计划对象
            
        Returns:
            Tuple[bool, str, Dict]: (是否成功, 消息, 详细结果)
        """
        logger.info(f"🎯 开始智能提审计划 {campaign.campaign_id_qc}")
        
        # 1. 首先检查计划是否准备好提审
        ready, reason, status = self.status_checker.check_plan_ready_for_appeal(db, campaign)
        if not ready:
            logger.warning(f"⚠️ 计划 {campaign.campaign_id_qc} 未准备好提审: {reason}")
            return False, f"计划未准备好: {reason}", {
                'ready': False,
                'reason': reason,
                'status': status,
                'retry_count': 0
            }
        
        logger.success(f"✅ 计划 {campaign.campaign_id_qc} 已准备好提审: {reason}")
        
        # 2. 执行提审重试循环
        last_error = None
        for attempt in range(self.max_retries):
            logger.info(f"📤 第 {attempt + 1}/{self.max_retries} 次提审尝试")
            
            try:
                # 2.1 尝试文本指令提审
                success, message = self._try_text_appeal(campaign)
                if success:
                    logger.success(f"✅ 文本提审成功: {message}")
                    self._update_campaign_appeal_success(db, campaign, attempt + 1)
                    return True, f"文本提审成功(第{attempt + 1}次尝试)", {
                        'method': 'text',
                        'retry_count': attempt + 1,
                        'message': message
                    }
                
                logger.warning(f"⚠️ 文本提审失败: {message}")
                
                # 2.2 尝试浏览器自动化提审
                success, message = self._try_browser_appeal(campaign)
                if success:
                    logger.success(f"✅ 浏览器提审成功: {message}")
                    self._update_campaign_appeal_success(db, campaign, attempt + 1)
                    return True, f"浏览器提审成功(第{attempt + 1}次尝试)", {
                        'method': 'browser',
                        'retry_count': attempt + 1,
                        'message': message
                    }
                
                logger.warning(f"⚠️ 浏览器提审也失败: {message}")
                last_error = message
                
                # 2.3 重试延迟
                if attempt < self.max_retries - 1:
                    delay = self.retry_delays[attempt]
                    logger.info(f"⏳ 等待 {delay} 秒后重试...")
                    time.sleep(delay)
                
            except Exception as e:
                logger.error(f"❌ 提审尝试 {attempt + 1} 异常: {e}")
                last_error = str(e)
                
                if attempt < self.max_retries - 1:
                    delay = self.retry_delays[attempt]
                    logger.info(f"⏳ 异常后等待 {delay} 秒重试...")
                    time.sleep(delay)
        
        # 3. 所有重试都失败
        logger.error(f"❌ 计划 {campaign.campaign_id_qc} 提审彻底失败，已重试 {self.max_retries} 次")
        self._update_campaign_appeal_failed(db, campaign, last_error or "未知错误")
        
        return False, f"提审失败，已重试{self.max_retries}次: {last_error}", {
            'retry_count': self.max_retries,
            'last_error': last_error
        }
    
    def _try_text_appeal(self, campaign: Campaign) -> Tuple[bool, str]:
        """尝试文本指令提审"""
        try:
            from qianchuan_aw.services.production_appeal_service import create_production_appeal_service
            
            appeal_service = create_production_appeal_service(self.app_settings)
            
            # 构造计划数据
            plan_data = {
                'campaign_id': campaign.campaign_id_qc,
                'principal_name': campaign.account.principal.name,
                'account_id': campaign.account.account_id_qc
            }
            
            # 执行提审
            results = appeal_service.batch_appeal_for_account(
                campaign.account.principal.name,
                campaign.account.account_id_qc,
                [plan_data]
            )
            
            if results and len(results) > 0:
                result = results[0]
                return result['success'], result['message']
            else:
                return False, "未收到提审结果"
                
        except Exception as e:
            logger.error(f"文本提审异常: {e}")
            return False, f"文本提审异常: {e}"
    
    def _try_browser_appeal(self, campaign: Campaign) -> Tuple[bool, str]:
        """尝试浏览器自动化提审"""
        try:
            from qianchuan_aw.services.appeal_browser_service import perform_appeal_via_browser
            
            success, message = perform_appeal_via_browser(
                campaign.account.principal.name,
                campaign.account.account_id_qc,
                campaign.campaign_id_qc,
                self.app_settings
            )
            
            return success, message or "浏览器提审完成"
            
        except Exception as e:
            logger.error(f"浏览器提审异常: {e}")
            return False, f"浏览器提审异常: {e}"
    
    def _update_campaign_appeal_success(self, db: Session, campaign: Campaign, retry_count: int):
        """更新计划提审成功状态"""
        try:
            campaign.appeal_status = 'appeal_pending'
            campaign.appeal_attempt_count = retry_count
            campaign.first_appeal_at = datetime.now(timezone.utc)
            campaign.last_appeal_at = datetime.now(timezone.utc)
            campaign.appeal_error_message = None
            campaign.updated_at = datetime.now(timezone.utc)
            
            db.commit()
            logger.info(f"✅ 计划 {campaign.campaign_id_qc} 提审成功状态已更新")
            
        except Exception as e:
            logger.error(f"更新计划提审成功状态失败: {e}")
            db.rollback()
    
    def _update_campaign_appeal_failed(self, db: Session, campaign: Campaign, error_message: str):
        """更新计划提审失败状态"""
        try:
            campaign.appeal_status = 'submission_failed'
            campaign.appeal_error_message = error_message[:500] if error_message else '提审失败'
            campaign.updated_at = datetime.now(timezone.utc)
            
            db.commit()
            logger.info(f"⚠️ 计划 {campaign.campaign_id_qc} 提审失败状态已更新")
            
        except Exception as e:
            logger.error(f"更新计划提审失败状态失败: {e}")
            db.rollback()
    
    def batch_smart_appeal(self, db: Session, campaigns: List[Campaign]) -> Dict[str, Any]:
        """
        批量智能提审
        
        Args:
            db: 数据库会话
            campaigns: 计划列表
            
        Returns:
            Dict[str, Any]: 批量提审结果
        """
        logger.info(f"🚀 开始批量智能提审 {len(campaigns)} 个计划")
        
        results = {
            'total': len(campaigns),
            'success': 0,
            'failed': 0,
            'details': []
        }
        
        for i, campaign in enumerate(campaigns, 1):
            logger.info(f"📋 处理第 {i}/{len(campaigns)} 个计划: {campaign.campaign_id_qc}")
            
            success, message, detail = self.smart_appeal_with_retry(db, campaign)
            
            result_detail = {
                'campaign_id': campaign.campaign_id_qc,
                'success': success,
                'message': message,
                'detail': detail
            }
            
            results['details'].append(result_detail)
            
            if success:
                results['success'] += 1
                logger.success(f"✅ 计划 {campaign.campaign_id_qc} 提审成功")
            else:
                results['failed'] += 1
                logger.error(f"❌ 计划 {campaign.campaign_id_qc} 提审失败: {message}")
            
            # 计划间隔
            if i < len(campaigns):
                logger.info("⏳ 等待3秒后处理下一个计划...")
                time.sleep(3)
        
        success_rate = (results['success'] / results['total'] * 100) if results['total'] > 0 else 0
        logger.info(f"🎉 批量智能提审完成: 成功 {results['success']}/{results['total']} ({success_rate:.1f}%)")
        
        return results
    
    def wait_and_appeal_when_ready(self, db: Session, campaigns: List[Campaign], 
                                 max_wait_minutes: int = 10) -> Dict[str, Any]:
        """
        等待计划准备好后进行提审
        
        Args:
            db: 数据库会话
            campaigns: 计划列表
            max_wait_minutes: 最大等待时间
            
        Returns:
            Dict[str, Any]: 提审结果
        """
        logger.info(f"⏳ 等待计划准备好后进行提审，最大等待 {max_wait_minutes} 分钟")
        
        # 1. 等待计划准备好
        ready_campaigns = self.status_checker.wait_for_plans_ready(
            db, campaigns, max_wait_minutes
        )
        
        if not ready_campaigns:
            logger.warning("⚠️ 没有计划准备好提审")
            return {
                'total': len(campaigns),
                'ready': 0,
                'success': 0,
                'failed': len(campaigns),
                'message': '没有计划准备好提审'
            }
        
        # 2. 对准备好的计划进行提审
        appeal_results = self.batch_smart_appeal(db, ready_campaigns)
        
        # 3. 合并结果
        final_results = {
            'total': len(campaigns),
            'ready': len(ready_campaigns),
            'not_ready': len(campaigns) - len(ready_campaigns),
            'success': appeal_results['success'],
            'failed': appeal_results['failed'] + (len(campaigns) - len(ready_campaigns)),
            'details': appeal_results['details']
        }
        
        return final_results


def create_smart_appeal_service(app_settings: Dict[str, Any]) -> SmartAppealService:
    """创建智能提审服务实例"""
    return SmartAppealService(app_settings)


# 测试函数
def test_smart_appeal_service():
    """测试智能提审服务"""
    from qianchuan_aw.utils.config_manager import get_settings
    from qianchuan_aw.utils.db_utils import database_session
    
    app_settings = get_settings()
    service = create_smart_appeal_service(app_settings)
    
    with database_session() as db:
        # 获取提审失败的计划
        failed_campaigns = db.query(Campaign).filter(
            Campaign.appeal_status == 'submission_failed'
        ).all()
        
        if failed_campaigns:
            logger.info(f"测试智能提审服务，处理 {len(failed_campaigns)} 个失败计划")
            results = service.wait_and_appeal_when_ready(db, failed_campaigns)
            
            logger.info(f"提审结果: {results}")
        else:
            logger.info("没有找到提审失败的计划")


if __name__ == "__main__":
    test_smart_appeal_service()
