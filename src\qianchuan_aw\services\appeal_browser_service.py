# -*- coding: utf-8 -*-
"""
[V64.0] 超时保护的申诉服务

解决阻塞问题，确保提审操作不会影响主工作流
"""
import os
import time
import signal
import multiprocessing
from typing import Tuple, Optional, Dict, Any
from qianchuan_aw.utils.logger import logger

class TimeoutError(Exception):
    """超时异常"""
    pass

def timeout_handler(signum, frame):
    """超时信号处理器"""
    raise TimeoutError("操作超时")

def execute_appeal_with_timeout(principal_name: str, account_id_qc: int, plan_id: int, app_settings: Dict[str, Any], timeout: int = 120) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    执行带超时保护的申诉操作
    
    Args:
        principal_name: 主体名称
        account_id_qc: 账户ID
        plan_id: 计划ID
        app_settings: 应用设置
        timeout: 超时时间（秒），默认2分钟
    
    Returns:
        (success, result)
    """
    logger.info(f"[{principal_name}] 开始超时保护申诉，计划ID: {plan_id}，超时: {timeout}秒")
    
    try:
        # 设置超时信号
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout)
        
        try:
            # 执行申诉操作
            from qianchuan_aw.services.bulletproof_appeal_service import perform_bulletproof_appeal
            
            success, result = perform_bulletproof_appeal(
                principal_name=principal_name,
                account_id_qc=account_id_qc,
                plan_id=plan_id,
                app_settings=app_settings
            )
            
            # 取消超时信号
            signal.alarm(0)
            
            logger.info(f"[{principal_name}] 申诉完成，计划ID: {plan_id}，结果: {'成功' if success else '失败'}")
            return success, result
            
        except TimeoutError:
            logger.error(f"[{principal_name}] 申诉超时，计划ID: {plan_id}，超时时间: {timeout}秒")
            return False, {"error": "timeout", "timeout_seconds": timeout}
            
        except Exception as e:
            logger.error(f"[{principal_name}] 申诉异常，计划ID: {plan_id}，错误: {e}")
            return False, {"error": str(e)}
            
        finally:
            # 确保取消超时信号
            signal.alarm(0)
    
    except Exception as e:
        logger.error(f"[{principal_name}] 超时保护设置失败，计划ID: {plan_id}，错误: {e}")
        return False, {"error": f"timeout_setup_failed: {e}"}

def execute_appeal_in_process(principal_name: str, account_id_qc: int, plan_id: int, app_settings: Dict[str, Any], timeout: int = 120) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    在独立进程中执行申诉操作（更强的隔离）
    
    Args:
        principal_name: 主体名称
        account_id_qc: 账户ID
        plan_id: 计划ID
        app_settings: 应用设置
        timeout: 超时时间（秒），默认2分钟
    
    Returns:
        (success, result)
    """
    logger.info(f"[{principal_name}] 开始进程隔离申诉，计划ID: {plan_id}，超时: {timeout}秒")
    
    def worker_function(queue, principal_name, account_id_qc, plan_id, app_settings):
        """工作进程函数"""
        try:
            # 在子进程中执行申诉
            from qianchuan_aw.services.bulletproof_appeal_service import perform_bulletproof_appeal
            
            success, result = perform_bulletproof_appeal(
                principal_name=principal_name,
                account_id_qc=account_id_qc,
                plan_id=plan_id,
                app_settings=app_settings
            )
            
            queue.put({"success": success, "result": result})
            
        except Exception as e:
            queue.put({"success": False, "result": {"error": str(e)}})
    
    try:
        # 创建进程间通信队列
        queue = multiprocessing.Queue()
        
        # 启动工作进程
        process = multiprocessing.Process(
            target=worker_function,
            args=(queue, principal_name, account_id_qc, plan_id, app_settings)
        )
        process.start()
        
        # 等待结果或超时
        process.join(timeout=timeout)
        
        if process.is_alive():
            # 进程仍在运行，强制终止
            logger.warning(f"[{principal_name}] 申诉进程超时，强制终止，计划ID: {plan_id}")
            process.terminate()
            process.join(timeout=5)
            
            if process.is_alive():
                # 强制杀死进程
                process.kill()
                process.join()
            
            return False, {"error": "process_timeout", "timeout_seconds": timeout}
        
        # 获取结果
        if not queue.empty():
            result = queue.get()
            success = result["success"]
            data = result["result"]
            
            logger.info(f"[{principal_name}] 进程申诉完成，计划ID: {plan_id}，结果: {'成功' if success else '失败'}")
            return success, data
        else:
            logger.error(f"[{principal_name}] 进程申诉无结果，计划ID: {plan_id}")
            return False, {"error": "no_result"}
    
    except Exception as e:
        logger.error(f"[{principal_name}] 进程申诉异常，计划ID: {plan_id}，错误: {e}")
        return False, {"error": f"process_error: {e}"}

def execute_quick_appeal(principal_name: str, account_id_qc: int, plan_id: int, app_settings: Dict[str, Any]) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    执行快速申诉（仅文本模式，30秒超时）
    
    Args:
        principal_name: 主体名称
        account_id_qc: 账户ID
        plan_id: 计划ID
        app_settings: 应用设置
    
    Returns:
        (success, result)
    """
    logger.info(f"[{principal_name}] 开始快速申诉，计划ID: {plan_id}")
    
    try:
        from qianchuan_aw.services.copilot_service import SimpleCopilotSession
        
        # 使用30秒超时
        with SimpleCopilotSession(principal_name, account_id_qc, app_settings) as session:
            success, result = session.appeal_via_text_command(plan_id)
            
            logger.info(f"[{principal_name}] 快速申诉完成，计划ID: {plan_id}，结果: {'成功' if success else '失败'}")
            
            if success:
                return True, {"status": "success", "plan_id": plan_id, "result": result}
            else:
                return False, {"status": "failed", "plan_id": plan_id, "error": result}
    
    except Exception as e:
        logger.error(f"[{principal_name}] 快速申诉异常，计划ID: {plan_id}，错误: {e}")
        return False, {"error": str(e)}

def smart_appeal(principal_name: str, account_id_qc: int, plan_id: int, app_settings: Dict[str, Any]) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    智能申诉策略：先快速文本，失败后降级到超时保护浏览器模式
    
    Args:
        principal_name: 主体名称
        account_id_qc: 账户ID
        plan_id: 计划ID
        app_settings: 应用设置
    
    Returns:
        (success, result)
    """
    logger.info(f"[{principal_name}] 开始智能申诉，计划ID: {plan_id}")
    
    # 第一步：尝试快速文本申诉
    logger.info(f"[{principal_name}] 步骤1: 尝试快速文本申诉，计划ID: {plan_id}")
    try:
        success, result = execute_quick_appeal(principal_name, account_id_qc, plan_id, app_settings)
        
        if success:
            logger.success(f"[{principal_name}] 快速文本申诉成功，计划ID: {plan_id}")
            return success, result
        else:
            logger.warning(f"[{principal_name}] 快速文本申诉失败，计划ID: {plan_id}，原因: {result}")
    
    except Exception as e:
        logger.warning(f"[{principal_name}] 快速文本申诉异常，计划ID: {plan_id}，错误: {e}")
    
    # 第二步：降级到超时保护浏览器申诉
    logger.info(f"[{principal_name}] 步骤2: 降级到超时保护浏览器申诉，计划ID: {plan_id}")
    try:
        # 通过 BrowserGateway 使用子进程模式（具备 idle 回收），保留120秒总超时
        from qianchuan_aw.services.browser_gateway import browser_context
        try:
            from qianchuan_aw.services.bulletproof_appeal_service import _execute_bulletproof_appeal_flow, _load_cookies
            cookies = _load_cookies(principal_name)
        except Exception:
            cookies = None

        success = False
        result = None

        import time
        start_ts = time.time()
        with browser_context(principal_name, app_settings, mode='subprocess') as ctx:
            page = ctx.new_page()
            page.set_default_timeout(30000)
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={account_id_qc}"
            page.goto(url, wait_until="domcontentloaded")
            if cookies:
                try:
                    # 某些适配器不暴露 context cookies；此处保守：已在 create_context 时加了cookies可忽略
                    pass
                except Exception:
                    pass

            # 这里直接调用“防弹级流程”的核心步骤，避免重复登录与弹窗干扰
            success, result = _execute_bulletproof_appeal_flow(page, plan_id)

            # 超时保护（与 execute_appeal_in_process 一致的 120 秒逻辑）
            if time.time() - start_ts > 120:
                success, result = False, {"error": "process_timeout", "timeout_seconds": 120}

        
        if success:
            logger.success(f"[{principal_name}] 浏览器申诉成功，计划ID: {plan_id}")
        else:
            logger.error(f"[{principal_name}] 浏览器申诉失败，计划ID: {plan_id}，原因: {result}")
        
        return success, result
    
    except Exception as e:
        logger.error(f"[{principal_name}] 浏览器申诉异常，计划ID: {plan_id}，错误: {e}")
        return False, {"error": str(e)}

# 兼容性接口
def perform_appeal_via_browser(principal_name: str, account_id_qc: int, plan_id: int, app_settings: Dict[str, Any]) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """兼容性接口，使用智能申诉策略"""
    return smart_appeal(principal_name, account_id_qc, plan_id, app_settings)
