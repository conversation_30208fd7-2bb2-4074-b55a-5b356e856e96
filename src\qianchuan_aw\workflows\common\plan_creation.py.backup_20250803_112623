# -*- coding: utf-8 -*-
"""
@Project: qianchuangzl
@File   : plan_creation.py
<AUTHOR> AI Assistant
@Date   : 2025/6/17
@Desc   : V13.0 - 动态组合式计划创建模块
"""
import os
import json
import datetime
import random
import sys
from typing import List, Optional
# 导入全局唯一性检查函数
# 移除全局导入以避免循环导入


from sqlalchemy.orm import Session

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import Principal, AdAccount, PlatformCreative, LocalCreative, Campaign
from qianchuan_aw.sdk_qc.client import QianchuanClient, AccountBlockedException
from qianchuan_aw.utils.workflow_helpers import get_random_titles, rollback_and_reset_creatives


def generate_plan_name(
    creatives: List[PlatformCreative],
    campaign_scene: str,
    bid_type: str,
    is_lab_ad: bool
) -> str:
    """
    [V31.0] 根据计划类型动态生成计划名称。
    """
    try:
        lab_part = "托管" if is_lab_ad else "自定义"
        bid_part = "ROI" if bid_type == "ROI" else "成交"
        scene_part = "新客" if campaign_scene == "NEW_CUSTOMER_TRANSFORMATION" else "日常"
        plan_type_tag = f"{lab_part}{bid_part}-{scene_part}"

        now = datetime.datetime.now()
        date_str = now.strftime("%m.%d")
        time_str = now.strftime("%H-%M")
        
        author = "未知作者"
        if creatives and creatives[0].local_creative and creatives[0].local_creative.file_path:
            filename = os.path.basename(creatives[0].local_creative.file_path)
            parts = filename.split('-')
            if len(parts) > 1:
                author = parts[1]

        rand_str = ''.join(random.choices('**********', k=4))
        
        plan_name = f"{date_str}/{plan_type_tag}/{author}/{date_str}/{time_str}-{rand_str}"
        return plan_name
        
    except Exception as e:
        logger.error(f"生成计划名称失败: {e}", exc_info=True)
        return f"自定义计划_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"


def create_ad_plan_enhanced(
    db: Session,
    principal: Principal,
    account: AdAccount,
    platform_creatives: List[PlatformCreative],
    campaign_scene: str,
    bid_type: str,
    is_lab_ad: bool,
    budget: float,
    app_settings: dict,
    cpa_bid: Optional[float] = None,
    roi_goal: Optional[float] = None,
    skip_db_operations: bool = False,
    enhancer=None
) -> dict:
    """
    [Enhanced] 创建广告计划 - 增强可靠性版本
    返回包含成功状态、计划ID和验证结果的字典
    """
    if enhancer is None:
        import sys
        from pathlib import Path

        # 添加项目根目录到Python路径
        project_root = Path(__file__).parent.parent.parent.parent.parent
        if str(project_root) not in sys.path:
            sys.path.insert(0, str(project_root))

        from ai_tools.enhancement.ai_tool_20250731_enhancement_plan_creation_reliability import PlanCreationReliabilityEnhancer
        enhancer = PlanCreationReliabilityEnhancer(app_settings)

    plan_name = generate_plan_name(
        creatives=platform_creatives,
        campaign_scene=campaign_scene,
        bid_type=bid_type,
        is_lab_ad=is_lab_ad
    )

    logger.info(f"[Enhanced] 准备创建计划 '{plan_name}'...")
    logger.info(f"  - 策略: 场景({campaign_scene}), 出价({bid_type}), 托管({is_lab_ad})")
    logger.info(f"  - 素材数量: {len(platform_creatives)}")

    try:
        # 执行核心创建逻辑
        result = _execute_plan_creation_core_logic(
            db, principal, account, platform_creatives, campaign_scene,
            bid_type, is_lab_ad, budget, app_settings, cpa_bid, roi_goal,
            skip_db_operations, plan_name, enhancer
        )

        return result

    except Exception as e:
        logger.error(f"增强版计划创建失败 (Plan: {plan_name}): {e}", exc_info=True)
        return {
            'success': False,
            'error': str(e),
            'plan_id': None,
            'campaign': None,
            'verified': False
        }


def create_ad_plan(
    db: Session,
    principal: Principal,
    account: AdAccount,
    platform_creatives: List[PlatformCreative],
    campaign_scene: str,
    bid_type: str,
    is_lab_ad: bool,
    budget: float,
    app_settings: dict, # 添加 app_settings 参数
    cpa_bid: Optional[float] = None,
    roi_goal: Optional[float] = None,
    skip_db_operations: bool = False
):
    # 🛡️ 测试视频全局唯一性检查 - 测试视频工作流铁律
    if account.account_type == 'TEST':
        try:
            from qianchuan_aw.workflows.scheduler import check_test_video_global_uniqueness

            # 检查所有关联的LocalCreative是否违反全局唯一性
            for platform_creative in platform_creatives:
                local_creative_id = None
                filename = "未知文件"

                if hasattr(platform_creative, 'local_creative_id'):
                    local_creative_id = platform_creative.local_creative_id
                elif hasattr(platform_creative, 'local_creative') and platform_creative.local_creative:
                    local_creative_id = platform_creative.local_creative.id
                    filename = platform_creative.local_creative.filename

                if local_creative_id:
                    if not check_test_video_global_uniqueness(local_creative_id, 'TEST'):
                        logger.error(f"🚨 测试视频工作流铁律阻止：测试视频 {filename} (ID: {local_creative_id}) 违反全局唯一性约束")
                        logger.error(f"   账户: {account.name} (类型: {account.account_type})")
                        logger.error(f"   该视频已在其他测试户中被使用，根据业务铁律禁止重复使用")
                        return None  # 阻止计划创建

        except Exception as uniqueness_error:
            logger.error(f"❌ 全局唯一性检查失败: {uniqueness_error}")
            # 为了安全，如果检查失败且是测试户，则阻止创建
            logger.error(f"   为确保业务规则执行，阻止测试户计划创建")
            return None

    plan_name = generate_plan_name(
        creatives=platform_creatives,
        campaign_scene=campaign_scene,
        bid_type=bid_type,
        is_lab_ad=is_lab_ad
    )
    logger.info(f"准备创建计划 '{plan_name}'...")
    logger.info(f"  - 策略: 场景({campaign_scene}), 出价({bid_type}), 托管({is_lab_ad})")

    try:
        # 移除旧的 sys.path 操作和 config 导入
        # project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
        # sys.path.insert(0, project_root)
        # from config import settings
        # sys.path.pop(0)

        client = QianchuanClient(app_id=app_settings['api_credentials']['app_id'], secret=app_settings['api_credentials']['secret'], principal_id=principal.id)

        # 重新计算 project_root，以确保正确加载 plan_templates.json
        current_file_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.abspath(os.path.join(current_file_dir, '..', '..', '..', '..')) # 从 src/qianchuan_aw/workflows/common 到 project_root

        with open(os.path.join(project_root, 'config', 'plan_templates.json'), 'r', encoding='utf-8') as f:
            templates = json.load(f)
        
        base_template_name = "base_new_customer" if campaign_scene == "NEW_CUSTOMER_TRANSFORMATION" else "base_daily_sale"
        plan_config = templates.get(base_template_name, {}).copy()
        if not plan_config:
            logger.error(f"基础模板 '{base_template_name}' 未找到！")
            return None

        plan_config.update({
            "advertiser_id": int(account.account_id_qc),
            "name": plan_name,
            "campaign_scene": campaign_scene,
            "lab_ad_type": "LAB_AD" if is_lab_ad else "NOT_LAB_AD",
            "creative_material_mode": "PROGRAMMATIC_CREATIVE"
        })
        # 只有当 account.aweme_id 存在时才设置
        if account.aweme_id:
            plan_config["aweme_id"] = int(account.aweme_id)

        delivery_setting = plan_config.get("delivery_setting", {})
        delivery_setting.update({
            "budget_mode": "BUDGET_MODE_DAY",
            "budget": budget,
            "live_schedule_type": "SCHEDULE_FROM_NOW",
        })
        
        if bid_type == "DEAL":
            delivery_setting["external_action"] = "AD_CONVERT_TYPE_LIVE_SUCCESSORDER_PAY"
            if cpa_bid: delivery_setting["cpa_bid"] = cpa_bid
        elif bid_type == "ROI":
            delivery_setting.update({
                "external_action": "AD_CONVERT_TYPE_LIVE_SUCCESSORDER_PAY",
                "deep_external_action": "AD_CONVERT_TYPE_LIVE_PAY_ROI",
                "deep_bid_type": "MIN"
            })
            if roi_goal: delivery_setting["roi_goal"] = roi_goal
        
        plan_config["marketing_scene"] = "FEED"
        plan_config["is_homepage_hide"] = 1
        delivery_setting["smart_bid_type"] = "SMART_BID_CUSTOM"
        
        plan_config["delivery_setting"] = delivery_setting

        if not is_lab_ad:
            campaign_groups = client.get_campaign_group_list(advertiser_id=account.account_id_qc)
            if not campaign_groups:
                logger.error(f"账户 {account.name} 下没有任何广告组，无法创建自定义计划。")
                return None
            plan_config["campaign_id"] = random.choice(campaign_groups)['id']

        title_materials = get_random_titles(count=app_settings['workflow']['titles_per_plan'], app_settings=app_settings) # 传入 app_settings
        if not title_materials:
            logger.error("无法从标题库获取标题，计划创建中止。")
            return None
        
        # 核心修复：在创建计划前，对素材列表根据 video_id 进行去重
        unique_creatives = []
        seen_video_ids = set()
        for creative in platform_creatives:
            # 使用 video_id 作为唯一标识符
            if creative.video_id and creative.video_id not in seen_video_ids:
                unique_creatives.append(creative)
                seen_video_ids.add(creative.video_id)

        if len(unique_creatives) < len(platform_creatives):
            logger.warning(
                f"检测到计划 '{plan_name}' 中存在重复的 video_id，已去重。原始数量: {len(platform_creatives)}, 去重后数量: {len(unique_creatives)}"
            )

        # 如果去重后没有可用素材，则直接跳过
        if not unique_creatives:
            logger.error(f"计划 '{plan_name}' 在去重后没有可用的唯一素材，创建中止。")
            return None

        video_materials = [
            {"video_id": pc.video_id, "video_cover_id": pc.video_cover_id, "image_mode": "VIDEO_VERTICAL"}
            for pc in unique_creatives
        ]
        plan_config.update({
            "programmatic_creative_media_list": video_materials,
            "programmatic_creative_title_list": title_materials
        })

        plan_data = client.create_ad_plan(plan_config=plan_config)

        if not plan_data or 'ad_id' not in plan_data:
            logger.error(f"创建广告计划失败。API返回: {plan_data}")
            return None
        
        ad_id = plan_data.get('ad_id')
        logger.success(f"广告计划创建成功! Ad ID: {ad_id}, Name: {plan_name}")

        if skip_db_operations:
            logger.info("已跳过数据库操作。")
            return plan_data

        new_campaign = Campaign(
            campaign_id_qc=ad_id,
            account_id=account.id,
            status='AUDITING'
        )
        db.add(new_campaign)
        db.flush()  # 获取campaign.id

        # 显式创建关联记录，避免SQLAlchemy关系映射问题
        valid_platform_creatives = []
        for pc in platform_creatives:
            if pc and pc.id:  # 确保platform_creative有效
                try:
                    # 手动插入关联记录
                    from sqlalchemy import text
                    db.execute(text("""
                        INSERT INTO campaign_platform_creative_association
                        (campaign_id, platform_creative_id)
                        VALUES (:campaign_id, :platform_creative_id)
                    """), {
                        "campaign_id": new_campaign.id,
                        "platform_creative_id": pc.id
                    })
                    valid_platform_creatives.append(pc)
                    logger.info(f"成功关联素材 {pc.id} 到计划 {new_campaign.id}")
                except Exception as e:
                    logger.error(f"关联素材 {pc.id} 失败: {e}")
                    # 如果是唯一性约束违规，回滚当前事务并重新开始
                    if "素材唯一性测试铁律违规" in str(e):
                        logger.warning(f"素材 {pc.id} 违反唯一性约束，跳过该素材")
                        db.rollback()
                        # 重新开始事务
                        db.begin()
                        continue
                    else:
                        # 其他错误也回滚事务
                        db.rollback()
                        db.begin()

        # [V2025.07.24 - 业务逻辑修正版] 修正状态转换逻辑
        from qianchuan_aw.utils.workflow_status import WorkflowStatus

        for pc in valid_platform_creatives:
            if pc.local_creative_id:
                # 计划创建成功后，直接设置为测试待审核状态
                # 只允许从uploaded_pending_plan状态转换
                (
                    db.query(LocalCreative)
                    .filter(
                        LocalCreative.id == pc.local_creative_id,
                        LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value
                    )
                    .update({"status": WorkflowStatus.TESTING_PENDING_REVIEW.value}, synchronize_session=False)
                )

        db.commit()
        logger.info(f"计划 {ad_id} 的数据库记录已创建，并更新了 {len(valid_platform_creatives)} 个素材的状态。")
        return new_campaign

    except AccountBlockedException as e:
        logger.critical(f"账户熔断！账户 '{account.name}' ({account.account_id_qc}) 因撞审被平台限制。")
        logger.critical(f"错误详情: {e.message}")
        
        account.status = 'deleted'
        logger.warning(f"已将账户 '{account.name}' 的状态设置为 'deleted'。")
        
        rollback_and_reset_creatives(db, platform_creatives, f"账户 '{account.name}' 创建计划时被熔断", app_settings=app_settings) # 传入 app_settings
        
        return None

    except Exception as e:
        logger.error(f"创建计划 '{plan_name}' 过程中发生未知错误: {e}", exc_info=True)
        if not skip_db_operations:
            db.rollback()
        return None


def _execute_plan_creation_core_logic(
    db: Session, principal: Principal, account: AdAccount, platform_creatives: List[PlatformCreative],
    campaign_scene: str, bid_type: str, is_lab_ad: bool, budget: float, app_settings: dict,
    cpa_bid: Optional[float], roi_goal: Optional[float], skip_db_operations: bool,
    plan_name: str, enhancer
) -> dict:
    """
    [Core Logic] 计划创建的核心逻辑，包含验证和确认机制
    """
    try:
        # === 第一步：全局唯一性检查 ===
        if account.account_type == 'TEST':
            try:
                from qianchuan_aw.workflows.scheduler import check_test_video_global_uniqueness

                for platform_creative in platform_creatives:
                    local_creative_id = None
                    filename = "未知文件"

                    if hasattr(platform_creative, 'local_creative_id'):
                        local_creative_id = platform_creative.local_creative_id
                    elif hasattr(platform_creative, 'local_creative') and platform_creative.local_creative:
                        local_creative_id = platform_creative.local_creative.id
                        filename = platform_creative.local_creative.filename

                    if local_creative_id:
                        if not check_test_video_global_uniqueness(local_creative_id, 'TEST'):
                            error_msg = f"测试视频 {filename} (ID: {local_creative_id}) 违反全局唯一性约束"
                            logger.error(f"🚨 测试视频工作流铁律阻止：{error_msg}")
                            return {
                                'success': False,
                                'error': error_msg,
                                'plan_id': None,
                                'campaign': None,
                                'verified': False
                            }

            except Exception as uniqueness_error:
                error_msg = f"全局唯一性检查失败: {uniqueness_error}"
                logger.error(f"❌ {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'plan_id': None,
                    'campaign': None,
                    'verified': False
                }

        # === 第二步：准备API客户端和配置 ===
        client = QianchuanClient(
            app_id=app_settings['api_credentials']['app_id'],
            secret=app_settings['api_credentials']['secret'],
            principal_id=principal.id
        )

        # 加载计划模板
        current_file_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.abspath(os.path.join(current_file_dir, '..', '..', '..', '..'))

        with open(os.path.join(project_root, 'config', 'plan_templates.json'), 'r', encoding='utf-8') as f:
            templates = json.load(f)

        base_template_name = "base_new_customer" if campaign_scene == "NEW_CUSTOMER_TRANSFORMATION" else "base_daily_sale"
        plan_config = templates.get(base_template_name, {}).copy()
        if not plan_config:
            error_msg = f"基础模板 '{base_template_name}' 未找到"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'plan_id': None,
                'campaign': None,
                'verified': False
            }

        # === 第三步：配置计划参数 ===
        plan_config.update({
            "advertiser_id": int(account.account_id_qc),
            "name": plan_name,
            "campaign_scene": campaign_scene,
            "lab_ad_type": "LAB_AD" if is_lab_ad else "NOT_LAB_AD",
            "creative_material_mode": "PROGRAMMATIC_CREATIVE"
        })

        if account.aweme_id:
            plan_config["aweme_id"] = int(account.aweme_id)

        delivery_setting = plan_config.get("delivery_setting", {})
        delivery_setting.update({
            "budget_mode": "BUDGET_MODE_DAY",
            "budget": budget,
            "live_schedule_type": "SCHEDULE_FROM_NOW",
        })

        if bid_type == "DEAL":
            delivery_setting["external_action"] = "AD_CONVERT_TYPE_LIVE_SUCCESSORDER_PAY"
            if cpa_bid:
                delivery_setting["cpa_bid"] = cpa_bid
        elif bid_type == "ROI":
            delivery_setting.update({
                "external_action": "AD_CONVERT_TYPE_LIVE_SUCCESSORDER_PAY",
                "deep_external_action": "AD_CONVERT_TYPE_LIVE_PAY_ROI",
                "deep_bid_type": "MIN"
            })
            if roi_goal:
                delivery_setting["roi_goal"] = roi_goal

        plan_config["marketing_scene"] = "FEED"
        plan_config["is_homepage_hide"] = 1
        delivery_setting["smart_bid_type"] = "SMART_BID_CUSTOM"
        plan_config["delivery_setting"] = delivery_setting

        # === 第四步：处理广告组和素材 ===
        if not is_lab_ad:
            campaign_groups = client.get_campaign_group_list(advertiser_id=account.account_id_qc)
            if not campaign_groups:
                error_msg = f"账户 {account.name} 下没有任何广告组，无法创建自定义计划"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'plan_id': None,
                    'campaign': None,
                    'verified': False
                }
            plan_config["campaign_id"] = random.choice(campaign_groups)['id']

        title_materials = get_random_titles(count=app_settings['workflow']['titles_per_plan'], app_settings=app_settings)
        if not title_materials:
            error_msg = "无法从标题库获取标题，计划创建中止"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'plan_id': None,
                'campaign': None,
                'verified': False
            }

        # 素材去重
        unique_creatives = []
        seen_video_ids = set()
        for creative in platform_creatives:
            if creative.video_id and creative.video_id not in seen_video_ids:
                unique_creatives.append(creative)
                seen_video_ids.add(creative.video_id)

        if len(unique_creatives) < len(platform_creatives):
            logger.warning(f"检测到计划 '{plan_name}' 中存在重复的 video_id，已去重。原始数量: {len(platform_creatives)}, 去重后数量: {len(unique_creatives)}")

        if not unique_creatives:
            error_msg = f"计划 '{plan_name}' 在去重后没有可用的唯一素材"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'plan_id': None,
                'campaign': None,
                'verified': False
            }

        video_materials = [
            {"video_id": pc.video_id, "video_cover_id": pc.video_cover_id, "image_mode": "VIDEO_VERTICAL"}
            for pc in unique_creatives
        ]
        plan_config.update({
            "programmatic_creative_media_list": video_materials,
            "programmatic_creative_title_list": title_materials
        })

        # === 第五步：执行计划创建 ===
        logger.info(f"🚀 开始创建计划: {plan_name}")
        plan_data = client.create_ad_plan(plan_config=plan_config)

        if not plan_data or 'ad_id' not in plan_data:
            error_msg = f"创建广告计划失败。API返回: {plan_data}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'plan_id': None,
                'campaign': None,
                'verified': False
            }

        ad_id = plan_data.get('ad_id')
        logger.success(f"📤 计划创建API调用成功! Ad ID: {ad_id}, Name: {plan_name}")

        # === 第六步：计划创建成功验证 ===
        verification_enabled = app_settings.get('robustness', {}).get('upload_reliability', {}).get('verification_enabled', True)
        verification_result = {'verified': False}

        if verification_enabled:
            logger.info(f"🔍 验证计划创建结果: plan_id={ad_id}")
            verification_result = enhancer.verify_plan_creation_success(client, account.account_id_qc, ad_id, plan_name)

            if not verification_result.get('verified'):
                error_msg = f"计划创建验证失败: {verification_result.get('error', '未知验证错误')}"
                logger.warning(f"⚠️ {error_msg}")
                # 注意：这里不直接返回失败，因为计划可能已经创建成功，只是验证有问题

        # === 第七步：数据库操作 ===
        campaign = None
        if not skip_db_operations:
            campaign = _update_database_for_plan_creation(db, account, ad_id, platform_creatives, unique_creatives)

        logger.success(f"✅ 计划 '{plan_name}' 创建完成")
        logger.info(f"   Plan ID: {ad_id}")
        logger.info(f"   验证状态: {'✅ 已验证' if verification_result.get('verified') else '⚠️ 跳过验证或验证失败'}")
        logger.info(f"   素材数量: {len(unique_creatives)}")

        return {
            'success': True,
            'error': None,
            'plan_id': ad_id,
            'campaign': campaign,
            'verified': verification_result.get('verified', False),
            'verification_details': verification_result
        }

    except AccountBlockedException as e:
        error_msg = f"账户熔断！账户 '{account.name}' ({account.account_id_qc}) 因撞审被平台限制: {e.message}"
        logger.critical(error_msg)

        account.status = 'deleted'
        logger.warning(f"已将账户 '{account.name}' 的状态设置为 'deleted'。")

        rollback_and_reset_creatives(db, platform_creatives, f"账户 '{account.name}' 创建计划时被熔断", app_settings=app_settings)

        return {
            'success': False,
            'error': error_msg,
            'plan_id': None,
            'campaign': None,
            'verified': False
        }

    except Exception as e:
        error_msg = f"创建计划 '{plan_name}' 过程中发生未知错误: {e}"
        logger.error(error_msg, exc_info=True)
        if not skip_db_operations:
            db.rollback()

        return {
            'success': False,
            'error': error_msg,
            'plan_id': None,
            'campaign': None,
            'verified': False
        }


def _update_database_for_plan_creation(db: Session, account: AdAccount, ad_id: str,
                                     platform_creatives: List[PlatformCreative],
                                     unique_creatives: List[PlatformCreative]) -> Campaign:
    """更新数据库记录用于计划创建"""
    from qianchuan_aw.utils.workflow_status import WorkflowStatus

    # 创建Campaign记录
    new_campaign = Campaign(
        campaign_id_qc=ad_id,
        account_id=account.id,
        status='AUDITING'
    )
    db.add(new_campaign)
    db.flush()  # 获取campaign.id

    # 显式创建关联记录
    valid_platform_creatives = []
    for pc in platform_creatives:
        if pc and pc.id:
            try:
                from sqlalchemy import text
                db.execute(text("""
                    INSERT INTO campaign_platform_creative_association
                    (campaign_id, platform_creative_id)
                    VALUES (:campaign_id, :platform_creative_id)
                """), {
                    "campaign_id": new_campaign.id,
                    "platform_creative_id": pc.id
                })
                valid_platform_creatives.append(pc)
                logger.info(f"成功关联素材 {pc.id} 到计划 {new_campaign.id}")
            except Exception as e:
                logger.error(f"关联素材 {pc.id} 失败: {e}")
                if "素材唯一性测试铁律违规" in str(e):
                    logger.warning(f"素材 {pc.id} 违反唯一性约束，跳过该素材")
                    db.rollback()
                    db.begin()
                    continue
                else:
                    db.rollback()
                    db.begin()

    # 更新本地素材状态
    for pc in valid_platform_creatives:
        if pc.local_creative_id:
            (
                db.query(LocalCreative)
                .filter(
                    LocalCreative.id == pc.local_creative_id,
                    LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value
                )
                .update({"status": WorkflowStatus.TESTING_PENDING_REVIEW.value}, synchronize_session=False)
            )

    db.commit()
    logger.info(f"计划 {ad_id} 的数据库记录已创建，并更新了 {len(valid_platform_creatives)} 个素材的状态。")
    return new_campaign
