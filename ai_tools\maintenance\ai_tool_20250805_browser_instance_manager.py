#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 维护工具
生命周期: 永久保留
创建目的: 管理浏览器实例，防止实例泄漏和性能问题
清理条件: 成为系统核心组件后可归档
"""

import os
import sys
import time
import psutil
import threading
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from contextlib import contextmanager

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

class BrowserInstanceManager:
    """浏览器实例管理器 - 防止实例泄漏和性能问题"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.max_instances = 3  # 最大允许的浏览器实例数
        self.max_memory_mb = 1500  # 最大内存占用(MB)
        self.cleanup_interval = 300  # 清理间隔(秒)
        self.tracked_pids: Set[int] = set()
        self.last_cleanup = datetime.now()
        
        # 启动后台清理线程
        self._start_cleanup_thread()
    
    def _start_cleanup_thread(self):
        """启动后台清理线程"""
        def cleanup_worker():
            while True:
                try:
                    self.cleanup_excess_instances()
                    time.sleep(self.cleanup_interval)
                except Exception as e:
                    print(f"浏览器清理线程错误: {e}")
                    time.sleep(60)  # 出错后等待1分钟再重试
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def get_browser_processes(self) -> List[Dict]:
        """获取当前浏览器进程信息"""
        processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'create_time', 'cmdline']):
            try:
                if proc.info['name'] and any(browser in proc.info['name'].lower() 
                                           for browser in ['chrome', 'chromium', 'playwright']):
                    memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                    
                    processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'memory_mb': round(memory_mb, 1),
                        'create_time': proc.info['create_time'],
                        'age_minutes': (time.time() - proc.info['create_time']) / 60
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return sorted(processes, key=lambda x: x['create_time'], reverse=True)
    
    def cleanup_excess_instances(self) -> int:
        """清理多余的浏览器实例"""
        processes = self.get_browser_processes()
        
        if len(processes) <= self.max_instances:
            return 0
        
        # 计算总内存占用
        total_memory = sum(p['memory_mb'] for p in processes)
        
        print(f"🔍 发现 {len(processes)} 个浏览器进程，总内存: {total_memory:.1f}MB")
        
        killed_count = 0
        
        # 策略1: 如果进程数超过限制，杀死最老的进程
        if len(processes) > self.max_instances:
            excess_count = len(processes) - self.max_instances
            oldest_processes = processes[-excess_count:]  # 最老的进程
            
            for proc_info in oldest_processes:
                if self._kill_process(proc_info['pid']):
                    killed_count += 1
                    print(f"  ✅ 清理老进程 PID: {proc_info['pid']} (运行{proc_info['age_minutes']:.1f}分钟)")
        
        # 策略2: 如果内存占用过高，清理内存占用最大的进程
        if total_memory > self.max_memory_mb:
            memory_sorted = sorted(processes, key=lambda x: x['memory_mb'], reverse=True)
            
            for proc_info in memory_sorted:
                if total_memory <= self.max_memory_mb:
                    break
                
                if self._kill_process(proc_info['pid']):
                    total_memory -= proc_info['memory_mb']
                    killed_count += 1
                    print(f"  ✅ 清理高内存进程 PID: {proc_info['pid']} ({proc_info['memory_mb']:.1f}MB)")
        
        if killed_count > 0:
            print(f"🧹 清理完成，终止了 {killed_count} 个浏览器进程")
        
        self.last_cleanup = datetime.now()
        return killed_count
    
    def _kill_process(self, pid: int) -> bool:
        """安全地终止进程"""
        try:
            proc = psutil.Process(pid)
            proc.terminate()
            
            # 等待进程终止
            try:
                proc.wait(timeout=5)
            except psutil.TimeoutExpired:
                # 如果进程没有在5秒内终止，强制杀死
                proc.kill()
            
            return True
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return False
    
    def register_browser_process(self, pid: int):
        """注册浏览器进程"""
        self.tracked_pids.add(pid)
    
    def unregister_browser_process(self, pid: int):
        """注销浏览器进程"""
        self.tracked_pids.discard(pid)
    
    @contextmanager
    def managed_browser_context(self, principal_name: str, app_settings: Dict):
        """管理的浏览器上下文"""
        # 在创建新浏览器前检查是否需要清理
        if datetime.now() - self.last_cleanup > timedelta(minutes=5):
            self.cleanup_excess_instances()
        
        browser = None
        context = None
        
        try:
            from playwright.sync_api import sync_playwright
            
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(
                headless=app_settings.get('browser', {}).get('headless', True),
                args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
            )
            
            # 注册进程
            if hasattr(browser, '_impl') and hasattr(browser._impl, '_connection'):
                # 尝试获取浏览器进程PID（Playwright内部实现）
                pass
            
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            
            yield context
            
        except Exception as e:
            print(f"浏览器上下文创建失败: {e}")
            raise
        finally:
            # 确保资源清理
            try:
                if context:
                    context.close()
                if browser:
                    browser.close()
                if 'playwright' in locals():
                    playwright.stop()
            except Exception as e:
                print(f"浏览器资源清理失败: {e}")
    
    def get_status(self) -> Dict:
        """获取管理器状态"""
        processes = self.get_browser_processes()
        total_memory = sum(p['memory_mb'] for p in processes)
        
        return {
            'process_count': len(processes),
            'total_memory_mb': round(total_memory, 1),
            'max_instances': self.max_instances,
            'max_memory_mb': self.max_memory_mb,
            'last_cleanup': self.last_cleanup.isoformat(),
            'tracked_pids': len(self.tracked_pids),
            'status': 'HEALTHY' if len(processes) <= self.max_instances and total_memory <= self.max_memory_mb else 'OVERLOADED'
        }
    
    def force_cleanup_all(self) -> int:
        """强制清理所有浏览器进程"""
        processes = self.get_browser_processes()
        killed_count = 0
        
        print(f"🧹 强制清理所有 {len(processes)} 个浏览器进程...")
        
        for proc_info in processes:
            if self._kill_process(proc_info['pid']):
                killed_count += 1
                print(f"  ✅ 终止进程 PID: {proc_info['pid']}")
        
        self.tracked_pids.clear()
        print(f"🧹 强制清理完成，终止了 {killed_count} 个进程")
        return killed_count

# 全局实例
browser_manager = BrowserInstanceManager()

def get_browser_manager() -> BrowserInstanceManager:
    """获取浏览器管理器实例"""
    return browser_manager

@contextmanager
def managed_browser_context(principal_name: str, app_settings: Dict):
    """便捷的管理浏览器上下文函数"""
    with browser_manager.managed_browser_context(principal_name, app_settings) as context:
        yield context

def cleanup_browser_instances():
    """清理浏览器实例的便捷函数"""
    return browser_manager.cleanup_excess_instances()

def get_browser_status():
    """获取浏览器状态的便捷函数"""
    return browser_manager.get_status()

def main():
    """主函数 - 用于测试和手动清理"""
    print("🎯 浏览器实例管理器")
    print("="*50)
    
    status = browser_manager.get_status()
    print(f"📊 当前状态: {status['status']}")
    print(f"🖥️ 浏览器进程: {status['process_count']} 个")
    print(f"💾 内存占用: {status['total_memory_mb']} MB")
    print(f"🧹 上次清理: {status['last_cleanup']}")
    
    if status['status'] == 'OVERLOADED':
        print("\n⚠️ 系统过载，执行清理...")
        killed = browser_manager.cleanup_excess_instances()
        print(f"✅ 清理完成，终止了 {killed} 个进程")
    else:
        print("\n✅ 系统状态正常")

if __name__ == "__main__":
    main()
