#!/usr/bin/env python3
"""
系统健康监控脚本
每10分钟运行一次，检测和修复异常状态
"""

import psycopg2
from datetime import datetime, timedelta
from loguru import logger

def check_system_health():
    """检查系统健康状态"""
    try:
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            database='qianchuan_analytics',
            user='lanfeng',
            password='zmx5062686'
        )
        
        cursor = conn.cursor()
        
        # 检查静默失败
        cursor.execute("""
            SELECT COUNT(*) FROM campaigns c
            JOIN ad_accounts aa ON c.account_id = aa.id
            WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
              AND c.status = 'AUDITING'
              AND c.first_appeal_at IS NULL
              AND c.created_at < NOW() - INTERVAL '30 minutes'
              AND c.appeal_status IS NULL
        """)
        
        silent_failures = cursor.fetchone()[0]
        
        if silent_failures > 0:
            logger.error(f"🚨 发现 {silent_failures} 个静默失败的计划！")
            
            # 自动修复
            cursor.execute("""
                UPDATE campaigns 
                SET appeal_status = 'appeal_pending',
                    last_updated = NOW()
                WHERE id IN (
                    SELECT c.id FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
                      AND c.status = 'AUDITING'
                      AND c.first_appeal_at IS NULL
                      AND c.created_at < NOW() - INTERVAL '30 minutes'
                      AND c.appeal_status IS NULL
                )
            """)
            
            fixed = cursor.rowcount
            conn.commit()
            logger.success(f"✅ 自动修复了 {fixed} 个静默失败")
        
        # 检查提审成功率
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN first_appeal_at IS NOT NULL THEN 1 END) as submitted
            FROM campaigns c
            JOIN ad_accounts aa ON c.account_id = aa.id
            WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
              AND c.created_at >= NOW() - INTERVAL '24 hours'
        """)
        
        result = cursor.fetchone()
        total, submitted = result
        
        if total > 0:
            success_rate = submitted / total * 100
            logger.info(f"📊 24小时提审成功率: {success_rate:.1f}% ({submitted}/{total})")
            
            if success_rate < 95:
                logger.error(f"🚨 提审成功率过低: {success_rate:.1f}%")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")

if __name__ == "__main__":
    check_system_health()
