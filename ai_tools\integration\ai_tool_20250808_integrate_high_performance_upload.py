#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 集成工具
生命周期: 永久保留
创建目的: 将高性能上传器集成到现有工作流中
清理条件: 集成完成后可归档
"""

import sys
import os
import shutil
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

class HighPerformanceUploadIntegrator:
    """高性能上传集成器"""
    
    def __init__(self):
        self.project_root = project_root
        self.integration_results = {
            'timestamp': datetime.now().isoformat(),
            'files_modified': [],
            'backups_created': [],
            'integration_points': []
        }

    def create_backup(self, file_path: Path) -> bool:
        """创建文件备份"""
        try:
            backup_dir = self.project_root / 'backups' / 'integration' / datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            backup_path = backup_dir / file_path.name
            shutil.copy2(file_path, backup_path)
            
            self.integration_results['backups_created'].append(str(backup_path))
            print(f"✅ 备份创建: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ 备份失败: {file_path} - {e}")
            return False

    def integrate_into_scheduler(self):
        """集成到调度器中"""
        print("🔧 集成高性能上传器到调度器...")
        
        scheduler_file = self.project_root / 'src' / 'qianchuan_aw' / 'workflows' / 'scheduler.py'
        
        if not scheduler_file.exists():
            print(f"❌ 调度器文件不存在: {scheduler_file}")
            return False
        
        # 创建备份
        if not self.create_backup(scheduler_file):
            return False
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经集成
            if 'HighPerformanceUploader' in content:
                print("✅ 高性能上传器已集成到调度器")
                return True
            
            # 添加导入
            import_line = "from qianchuan_aw.workflows.high_performance_uploader import HighPerformanceUploader"
            
            if import_line not in content:
                # 找到合适的位置添加导入
                lines = content.split('\n')
                import_index = -1
                
                for i, line in enumerate(lines):
                    if line.startswith('from qianchuan_aw.') and 'import' in line:
                        import_index = i
                
                if import_index >= 0:
                    lines.insert(import_index + 1, import_line)
                    content = '\n'.join(lines)
                    print("✅ 添加高性能上传器导入")
                else:
                    print("⚠️ 无法找到合适位置添加导入")
            
            # 保存修改
            with open(scheduler_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.integration_results['files_modified'].append(str(scheduler_file))
            self.integration_results['integration_points'].append('scheduler_import')
            
            return True
            
        except Exception as e:
            print(f"❌ 集成到调度器失败: {e}")
            return False

    def integrate_into_manual_launch(self):
        """集成到手动启动工具中"""
        print("🔧 集成高性能上传器到手动启动工具...")
        
        manual_launch_file = self.project_root / 'tools' / 'manual_launch.py'
        
        if not manual_launch_file.exists():
            print(f"❌ 手动启动文件不存在: {manual_launch_file}")
            return False
        
        # 创建备份
        if not self.create_backup(manual_launch_file):
            return False
        
        try:
            with open(manual_launch_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经集成
            if 'HighPerformanceUploader' in content:
                print("✅ 高性能上传器已集成到手动启动工具")
                return True
            
            # 添加高性能上传选项
            integration_code = '''
# 高性能批量上传功能
def batch_upload_with_high_performance(principal_name: str, account_ids: List[int], video_directory: str):
    """使用高性能上传器进行批量上传"""
    from qianchuan_aw.workflows.high_performance_uploader import HighPerformanceUploader
    from qianchuan_aw.utils.config import load_settings
    
    app_settings = load_settings()
    uploader = HighPerformanceUploader(app_settings)
    
    # 准备上传任务
    upload_tasks = []
    video_files = list(Path(video_directory).glob("*.mp4"))
    
    for video_file in video_files:
        for account_id in account_ids:
            upload_tasks.append({
                'file_path': str(video_file),
                'account_id': account_id,
                'principal_name': principal_name,
                'local_creative_id': None  # 需要从数据库获取或创建
            })
    
    print(f"🚀 开始高性能批量上传: {len(upload_tasks)} 个任务")
    results = uploader.batch_upload_videos(upload_tasks)
    
    success_count = sum(1 for r in results if r.get('success'))
    print(f"📊 上传完成: {success_count}/{len(results)} 成功")
    
    return results
'''
            
            # 找到合适位置添加函数
            if 'def batch_upload_with_high_performance' not in content:
                # 在文件末尾添加
                content += integration_code
                print("✅ 添加高性能批量上传函数")
            
            # 保存修改
            with open(manual_launch_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.integration_results['files_modified'].append(str(manual_launch_file))
            self.integration_results['integration_points'].append('manual_launch_function')
            
            return True
            
        except Exception as e:
            print(f"❌ 集成到手动启动工具失败: {e}")
            return False

    def create_performance_test_script(self):
        """创建性能测试脚本"""
        print("📊 创建性能测试脚本...")
        
        test_script_path = self.project_root / 'ai_tools' / 'testing' / 'ai_tool_20250808_upload_performance_test.py'
        test_script_path.parent.mkdir(parents=True, exist_ok=True)
        
        test_script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上传性能测试脚本
比较优化前后的上传性能
"""

import sys
import time
import random
from pathlib import Path
from datetime import datetime
from typing import List, Dict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

class UploadPerformanceTester:
    """上传性能测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': []
        }
    
    def test_traditional_upload(self, test_files: List[str], account_id: int, principal_name: str) -> Dict:
        """测试传统上传方式"""
        print("🔄 测试传统上传方式...")
        
        start_time = time.time()
        success_count = 0
        
        # 这里调用原有的上传逻辑
        # 由于需要实际的API调用，这里只是模拟
        for file_path in test_files:
            try:
                # 模拟上传时间
                time.sleep(random.uniform(15, 45))  # 15-45秒
                success_count += 1
            except Exception as e:
                print(f"传统上传失败: {file_path} - {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        result = {
            'method': 'traditional',
            'files_count': len(test_files),
            'success_count': success_count,
            'duration_seconds': duration,
            'throughput_per_minute': (success_count / duration) * 60 if duration > 0 else 0
        }
        
        print(f"📊 传统上传结果: {success_count}/{len(test_files)} 成功, 耗时 {duration:.1f}秒")
        return result
    
    def test_high_performance_upload(self, test_files: List[str], account_id: int, principal_name: str) -> Dict:
        """测试高性能上传方式"""
        print("🚀 测试高性能上传方式...")
        
        try:
            from qianchuan_aw.workflows.high_performance_uploader import HighPerformanceUploader
            from qianchuan_aw.utils.config import load_settings
            
            app_settings = load_settings()
            uploader = HighPerformanceUploader(app_settings)
            
            # 准备测试任务
            upload_tasks = []
            for file_path in test_files:
                upload_tasks.append({
                    'file_path': file_path,
                    'account_id': account_id,
                    'principal_name': principal_name,
                    'local_creative_id': 1  # 测试用ID
                })
            
            start_time = time.time()
            
            # 执行高性能上传
            results = uploader.batch_upload_videos(upload_tasks)
            
            end_time = time.time()
            duration = end_time - start_time
            
            success_count = sum(1 for r in results if r.get('success'))
            
            result = {
                'method': 'high_performance',
                'files_count': len(test_files),
                'success_count': success_count,
                'duration_seconds': duration,
                'throughput_per_minute': (success_count / duration) * 60 if duration > 0 else 0
            }
            
            print(f"📊 高性能上传结果: {success_count}/{len(test_files)} 成功, 耗时 {duration:.1f}秒")
            return result
            
        except Exception as e:
            print(f"❌ 高性能上传测试失败: {e}")
            return {
                'method': 'high_performance',
                'error': str(e)
            }
    
    def run_performance_comparison(self, test_directory: str, account_id: int, principal_name: str):
        """运行性能对比测试"""
        print("🎯 上传性能对比测试")
        print("="*50)
        
        # 获取测试文件
        test_files = list(Path(test_directory).glob("*.mp4"))[:10]  # 限制测试文件数量
        
        if not test_files:
            print(f"❌ 测试目录中没有找到视频文件: {test_directory}")
            return
        
        print(f"📁 测试文件: {len(test_files)} 个")
        
        # 测试传统方式
        traditional_result = self.test_traditional_upload(test_files, account_id, principal_name)
        self.test_results['tests'].append(traditional_result)
        
        print()
        
        # 测试高性能方式
        hp_result = self.test_high_performance_upload(test_files, account_id, principal_name)
        self.test_results['tests'].append(hp_result)
        
        # 对比结果
        print("\\n" + "="*50)
        print("📊 性能对比结果:")
        
        if 'error' not in traditional_result and 'error' not in hp_result:
            traditional_throughput = traditional_result['throughput_per_minute']
            hp_throughput = hp_result['throughput_per_minute']
            
            improvement = ((hp_throughput - traditional_throughput) / traditional_throughput * 100) if traditional_throughput > 0 else 0
            
            print(f"  传统方式: {traditional_throughput:.1f} 视频/分钟")
            print(f"  高性能方式: {hp_throughput:.1f} 视频/分钟")
            print(f"  性能提升: {improvement:.1f}%")
            
            time_traditional = traditional_result['duration_seconds']
            time_hp = hp_result['duration_seconds']
            time_savings = ((time_traditional - time_hp) / time_traditional * 100) if time_traditional > 0 else 0
            
            print(f"  时间节省: {time_savings:.1f}%")
        
        # 保存测试结果
        self.save_test_results()

    def save_test_results(self):
        """保存测试结果"""
        report_path = self.project_root / 'ai_reports' / 'testing' / f'upload_performance_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 测试结果已保存: {report_path}")

def main():
    """主函数"""
    tester = UploadPerformanceTester()
    
    # 这里需要用户提供测试参数
    test_directory = input("请输入测试视频目录路径: ").strip()
    account_id = int(input("请输入测试账户ID: ").strip())
    principal_name = input("请输入主体名称: ").strip()
    
    tester.run_performance_comparison(test_directory, account_id, principal_name)

if __name__ == "__main__":
    main()
'''
        
        try:
            with open(test_script_path, 'w', encoding='utf-8') as f:
                f.write(test_script_content)
            
            print(f"✅ 性能测试脚本已创建: {test_script_path}")
            self.integration_results['files_modified'].append(str(test_script_path))
            return True
            
        except Exception as e:
            print(f"❌ 创建性能测试脚本失败: {e}")
            return False

    def generate_integration_report(self):
        """生成集成报告"""
        report_path = self.project_root / 'ai_reports' / 'integration' / f'ai_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}_high_performance_upload_integration.json'
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.integration_results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 集成报告已保存: {report_path}")

    def run_integration(self):
        """运行完整集成"""
        print("🎯 千川自动化项目 - 高性能上传器集成")
        print("📌 目标: 将高性能上传器集成到现有工作流")
        print("="*60)
        
        success_count = 0
        total_tasks = 3
        
        # 1. 集成到调度器
        if self.integrate_into_scheduler():
            success_count += 1
        print()
        
        # 2. 集成到手动启动工具
        if self.integrate_into_manual_launch():
            success_count += 1
        print()
        
        # 3. 创建性能测试脚本
        if self.create_performance_test_script():
            success_count += 1
        print()
        
        # 4. 生成报告
        self.generate_integration_report()
        
        # 5. 总结
        print("="*60)
        print("📊 集成总结:")
        print(f"  ✅ 成功任务: {success_count}/{total_tasks}")
        print(f"  📁 修改文件: {len(self.integration_results['files_modified'])} 个")
        print(f"  💾 创建备份: {len(self.integration_results['backups_created'])} 个")
        print(f"  🔗 集成点: {len(self.integration_results['integration_points'])} 个")
        
        if success_count == total_tasks:
            print("\\n🎉 集成完成！高性能上传器已成功集成到系统中")
            print("💡 下一步:")
            print("1. 重启服务以应用集成")
            print("2. 运行性能测试验证效果")
            print("3. 根据测试结果进行调优")
        else:
            print("\\n⚠️ 集成部分成功，请检查失败的任务")
        
        return success_count == total_tasks

def main():
    """主函数"""
    integrator = HighPerformanceUploadIntegrator()
    
    print("🔧 这将修改现有代码文件以集成高性能上传器")
    print("所有修改的文件都会自动备份")
    print()
    
    try:
        response = input("是否继续执行集成? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ 用户取消操作")
            return
    except KeyboardInterrupt:
        print("\\n❌ 用户中断操作")
        return
    
    success = integrator.run_integration()
    
    if success:
        print("\\n🎉 集成成功！")
    else:
        print("\\n❌ 集成失败，请检查错误信息")

if __name__ == "__main__":
    main()
