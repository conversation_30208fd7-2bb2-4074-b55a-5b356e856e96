#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 安全地重构工作流目录结构，将分散的工作流相关代码统一归类
清理条件: 重构完成并稳定运行后可归档
"""

"""
工作流目录重构工具
================

安全地将分散的工作流相关代码统一到工作流目录下，提高代码组织性和可维护性。

功能特性：
1. 渐进式迁移，确保系统稳定性
2. 自动创建新目录结构
3. 智能文件迁移和重命名
4. 自动更新导入路径
5. 创建兼容性层
6. 全面的备份和回滚机制

创建时间: 2025-08-03
版本: v1.0 - 工作流重构版
"""

import os
import shutil
import re
from pathlib import Path
from typing import Dict, List, Tuple
from loguru import logger


class WorkflowReorganizer:
    """工作流目录重构器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.workflows_dir = self.project_root / "src" / "qianchuan_aw" / "workflows"
        self.ai_tools_dir = self.project_root / "ai_tools"
        
        # 新目录结构定义
        self.new_structure = {
            'core': ['scheduler.py', 'tasks.py'],
            'enhancement': [
                'ai_tool_20250801_enhancement_workflow_integration.py',
                'ai_tool_20250801_enhancement_plan_status_checker.py', 
                'ai_tool_20250801_enhancement_smart_appeal_service.py',
                'ai_tool_20250801_enhancement_thread_safe_appeal_service.py',
                'ai_tool_20250801_enhancement_stability_monitor.py'
            ],
            'appeal': [
                'smart_appeal_scheduler.py',
                'enhanced_appeal_workflow.py', 
                'batch_appeal_system.py',
                'appeal_and_monitor.py'
            ],
            'harvest': [
                'independent_material_harvest.py',
                'realtime_harvest_workflow.py',
                'fast_monitor.py'
            ],
            'reliability': [
                'fault_tolerance_workflow.py',
                'workflow_self_healing.py',
                'smart_stagnation_detector.py'
            ],
            'common': [
                'flexible_grouping.py'
            ]
        }
        
        # 文件重命名映射
        self.rename_mapping = {
            'ai_tool_20250801_enhancement_workflow_integration.py': 'workflow_integration.py',
            'ai_tool_20250801_enhancement_plan_status_checker.py': 'plan_status_checker.py',
            'ai_tool_20250801_enhancement_smart_appeal_service.py': 'smart_appeal_service.py',
            'ai_tool_20250801_enhancement_thread_safe_appeal_service.py': 'thread_safe_appeal_service.py',
            'ai_tool_20250801_enhancement_stability_monitor.py': 'stability_monitor.py'
        }
        
    def create_backup(self) -> str:
        """创建完整备份"""
        logger.info("📦 创建完整备份...")
        
        backup_dir = self.project_root / f"backup_workflow_reorganization_{self._get_timestamp()}"
        backup_dir.mkdir(exist_ok=True)
        
        # 备份工作流目录
        if self.workflows_dir.exists():
            shutil.copytree(self.workflows_dir, backup_dir / "workflows")
            
        # 备份AI工具目录
        if self.ai_tools_dir.exists():
            shutil.copytree(self.ai_tools_dir / "enhancement", backup_dir / "ai_tools_enhancement")
            
        logger.success(f"✅ 备份创建完成: {backup_dir}")
        return str(backup_dir)
        
    def create_new_structure(self) -> bool:
        """创建新的目录结构"""
        logger.info("🏗️ 创建新目录结构...")
        
        try:
            # 创建主要子目录
            for subdir in self.new_structure.keys():
                new_dir = self.workflows_dir / subdir
                new_dir.mkdir(exist_ok=True)
                
                # 创建 __init__.py
                init_file = new_dir / "__init__.py"
                if not init_file.exists():
                    init_file.write_text('"""工作流子模块"""\n')
                    
            # 创建legacy目录用于备份文件
            legacy_dir = self.workflows_dir / "legacy" / "backups"
            legacy_dir.mkdir(parents=True, exist_ok=True)
            (self.workflows_dir / "legacy" / "__init__.py").write_text('"""遗留文件"""\n')
            
            logger.success("✅ 新目录结构创建完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建目录结构失败: {e}")
            return False
            
    def migrate_files(self) -> bool:
        """迁移文件到新位置"""
        logger.info("📁 开始文件迁移...")
        
        try:
            # 迁移AI增强工具
            self._migrate_ai_enhancement_tools()
            
            # 重组现有工作流文件
            self._reorganize_existing_workflows()
            
            # 移动备份文件
            self._move_backup_files()
            
            logger.success("✅ 文件迁移完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 文件迁移失败: {e}")
            return False
            
    def _migrate_ai_enhancement_tools(self):
        """迁移AI增强工具"""
        logger.info("🔧 迁移AI增强工具...")
        
        enhancement_source = self.ai_tools_dir / "enhancement"
        enhancement_target = self.workflows_dir / "enhancement"
        
        for filename in self.new_structure['enhancement']:
            source_file = enhancement_source / filename
            if source_file.exists():
                # 确定目标文件名
                target_filename = self.rename_mapping.get(filename, filename)
                target_file = enhancement_target / target_filename
                
                # 复制文件
                shutil.copy2(source_file, target_file)
                logger.info(f"   📄 {filename} → {target_filename}")
                
                # 更新文件内容
                self._update_file_content(target_file)
                
    def _reorganize_existing_workflows(self):
        """重组现有工作流文件"""
        logger.info("📋 重组现有工作流文件...")
        
        for subdir, files in self.new_structure.items():
            if subdir == 'enhancement':  # 已经处理过
                continue
                
            target_dir = self.workflows_dir / subdir
            
            for filename in files:
                source_file = self.workflows_dir / filename
                if source_file.exists():
                    target_file = target_dir / filename
                    
                    # 移动文件
                    shutil.move(str(source_file), str(target_file))
                    logger.info(f"   📄 {filename} → {subdir}/{filename}")
                    
    def _move_backup_files(self):
        """移动备份文件到legacy目录"""
        logger.info("🗂️ 整理备份文件...")
        
        backup_target = self.workflows_dir / "legacy" / "backups"
        
        # 查找所有备份文件
        backup_patterns = [
            "*.backup*",
            "*_backup_*",
            "scheduler.backup_*",
            "tasks.backup_*"
        ]
        
        for pattern in backup_patterns:
            for backup_file in self.workflows_dir.glob(pattern):
                if backup_file.is_file():
                    target_file = backup_target / backup_file.name
                    shutil.move(str(backup_file), str(target_file))
                    logger.info(f"   📄 {backup_file.name} → legacy/backups/")
                    
    def _update_file_content(self, file_path: Path):
        """更新文件内容，修复导入路径"""
        logger.debug(f"🔧 更新文件内容: {file_path}")
        
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # 更新AI工具文件头部信息
            if 'AI生成文件信息' in content:
                content = re.sub(
                    r'文件类型: 长期工具',
                    '文件类型: 工作流增强组件',
                    content
                )
                content = re.sub(
                    r'创建目的: .*',
                    '创建目的: 工作流增强功能，已集成到统一工作流目录',
                    content
                )
                
            # 更新内部导入路径
            content = self._fix_internal_imports(content)
            
            file_path.write_text(content, encoding='utf-8')
            
        except Exception as e:
            logger.warning(f"⚠️ 更新文件内容失败 {file_path}: {e}")
            
    def _fix_internal_imports(self, content: str) -> str:
        """修复内部导入路径"""
        
        # 修复AI工具间的相互导入
        import_fixes = [
            (r'from ai_tool_20250801_enhancement_plan_status_checker import', 
             'from .plan_status_checker import'),
            (r'from ai_tool_20250801_enhancement_smart_appeal_service import',
             'from .smart_appeal_service import'),
            (r'from ai_tool_20250801_enhancement_thread_safe_appeal_service import',
             'from .thread_safe_appeal_service import'),
        ]
        
        for old_pattern, new_pattern in import_fixes:
            content = re.sub(old_pattern, new_pattern, content)
            
        return content
        
    def update_main_imports(self) -> bool:
        """更新主要文件中的导入路径"""
        logger.info("🔗 更新主要导入路径...")
        
        try:
            # 更新scheduler.py中的导入
            self._update_scheduler_imports()
            
            # 更新tasks.py中的导入
            self._update_tasks_imports()
            
            logger.success("✅ 导入路径更新完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新导入路径失败: {e}")
            return False
            
    def _update_scheduler_imports(self):
        """更新scheduler.py中的导入"""
        scheduler_file = self.workflows_dir / "core" / "scheduler.py"
        
        if scheduler_file.exists():
            content = scheduler_file.read_text(encoding='utf-8')
            
            # 替换AI工具导入为新的相对导入
            new_import = """        # 使用工作流增强集成器
        from ..enhancement.workflow_integration import handle_plan_submission_smart"""
            
            content = re.sub(
                r'        # 使用智能工作流集成器.*?from ai_tool_20250801_enhancement_workflow_integration import handle_plan_submission_smart',
                new_import,
                content,
                flags=re.DOTALL
            )
            
            scheduler_file.write_text(content, encoding='utf-8')
            logger.info("   ✅ scheduler.py 导入路径已更新")
            
    def _update_tasks_imports(self):
        """更新tasks.py中的导入"""
        tasks_file = self.workflows_dir / "core" / "tasks.py"
        
        if tasks_file.exists():
            content = tasks_file.read_text(encoding='utf-8')
            
            # 更新scheduler导入
            content = re.sub(
                r'from qianchuan_aw\.workflows import scheduler',
                'from ..core import scheduler',
                content
            )
            
            tasks_file.write_text(content, encoding='utf-8')
            logger.info("   ✅ tasks.py 导入路径已更新")
            
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def execute_reorganization(self) -> bool:
        """执行完整的重构流程"""
        logger.info("🚀 开始工作流目录重构")
        logger.info("="*60)
        
        try:
            # 第一步：创建备份
            backup_path = self.create_backup()
            
            # 第二步：创建新目录结构
            if not self.create_new_structure():
                return False
                
            # 第三步：迁移文件
            if not self.migrate_files():
                return False
                
            # 第四步：更新导入路径
            if not self.update_main_imports():
                return False
                
            logger.success("🎉 工作流目录重构完成！")
            logger.info(f"📦 备份位置: {backup_path}")
            logger.info("💡 建议：运行测试确保所有功能正常")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 重构过程失败: {e}")
            return False


def main():
    """主函数"""
    logger.info("🏗️ 工作流目录重构工具")
    
    # 获取项目根目录
    project_root = os.getcwd()
    
    # 创建重构器
    reorganizer = WorkflowReorganizer(project_root)
    
    # 执行重构
    success = reorganizer.execute_reorganization()
    
    if success:
        logger.success("✅ 重构成功完成！")
    else:
        logger.error("❌ 重构失败，请检查日志")
        
    return success


if __name__ == "__main__":
    main()
