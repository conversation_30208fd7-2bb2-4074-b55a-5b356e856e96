#!/usr/bin/env python3
"""
工作流自愈系统
================
文件类型: 核心工作流组件
生命周期: 永久保留
创建目的: 确保视频工作流100%自主完美运行，无需外部监控
清理条件: 永不删除，系统核心组件
"""

from loguru import logger
from sqlalchemy.orm import Session
from sqlalchemy import text
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import Campaign, AdAccount


class WorkflowSelfHealing:
    """工作流自愈系统 - 确保100%自主运行"""
    
    def __init__(self):
        self.healing_stats = {
            'silent_failures_fixed': 0,
            'stuck_plans_fixed': 0,
            'status_inconsistencies_fixed': 0,
            'last_healing_time': None
        }
    
    def perform_self_healing(self, db: Session) -> Dict[str, int]:
        """执行自愈检查和修复"""
        logger.info("🔧 工作流自愈系统启动...")
        
        healing_results = {
            'silent_failures': self._fix_silent_failures(db),
            'stuck_plans': self._fix_stuck_plans(db),
            'status_inconsistencies': self._fix_status_inconsistencies(db),
            'orphaned_plans': self._fix_orphaned_plans(db)
        }
        
        total_fixed = sum(healing_results.values())
        
        if total_fixed > 0:
            logger.success(f"✅ 自愈完成，修复了 {total_fixed} 个问题")
            self._update_healing_stats(healing_results)
        else:
            logger.info("✅ 系统健康，无需修复")
        
        self.healing_stats['last_healing_time'] = datetime.now()
        
        return healing_results
    
    def _fix_silent_failures(self, db: Session) -> int:
        """修复静默失败 - 创建了但从未提审的计划"""
        try:
            # 查找静默失败的计划：状态为AUDITING但从未被提审，且创建时间超过30分钟
            # 修复：不再要求appeal_status为NULL，因为可能已经被设为appeal_pending但仍未实际提审
            silent_failures = db.execute(text("""
                SELECT COUNT(*) FROM campaigns c
                JOIN ad_accounts aa ON c.account_id = aa.id
                WHERE c.status = 'AUDITING'
                  AND c.first_appeal_at IS NULL
                  AND c.created_at < NOW() - INTERVAL '30 minutes'
            """)).scalar()

            if silent_failures > 0:
                logger.warning(f"🔧 发现 {silent_failures} 个静默失败的计划，正在修复...")

                # 确保这些计划的appeal_status正确设置
                db.execute(text("""
                    UPDATE campaigns
                    SET appeal_status = 'appeal_pending',
                        last_updated = NOW()
                    WHERE id IN (
                        SELECT c.id FROM campaigns c
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        WHERE c.status = 'AUDITING'
                          AND c.first_appeal_at IS NULL
                          AND c.created_at < NOW() - INTERVAL '30 minutes'
                    )
                """))

                db.commit()
                logger.warning(f"🔧 修复了 {silent_failures} 个静默失败的计划")

            return silent_failures
            
            if silent_failures > 0:
                db.commit()
                logger.warning(f"🔧 修复了 {silent_failures} 个静默失败的计划")
            
            return silent_failures
            
        except Exception as e:
            logger.error(f"❌ 修复静默失败时出错: {e}")
            db.rollback()
            return 0
    
    def _fix_stuck_plans(self, db: Session) -> int:
        """修复卡住的计划 - 长时间处于执行状态的计划"""
        try:
            # 查找卡住的计划：appeal_status为执行中但超过10分钟未更新
            stuck_plans = db.execute(text("""
                UPDATE campaigns 
                SET appeal_status = 'appeal_pending',
                    appeal_error_message = 'Auto-recovered from stuck status',
                    last_updated = NOW()
                WHERE id IN (
                    SELECT c.id FROM campaigns c
                    WHERE c.appeal_status IN ('appeal_executing', 'appealing')
                      AND c.last_updated < NOW() - INTERVAL '10 minutes'
                )
            """)).rowcount
            
            if stuck_plans > 0:
                db.commit()
                logger.warning(f"🔧 修复了 {stuck_plans} 个卡住的计划")
            
            return stuck_plans
            
        except Exception as e:
            logger.error(f"❌ 修复卡住计划时出错: {e}")
            db.rollback()
            return 0
    
    def _fix_status_inconsistencies(self, db: Session) -> int:
        """修复状态不一致 - 确保所有待处理计划都有正确的状态"""
        try:
            # 修复状态不一致：AUDITING状态但appeal_status为空的计划
            inconsistencies = db.execute(text("""
                UPDATE campaigns 
                SET appeal_status = 'appeal_pending',
                    last_updated = NOW()
                WHERE status = 'AUDITING'
                  AND (appeal_status IS NULL OR appeal_status = '')
                  AND first_appeal_at IS NULL
            """)).rowcount
            
            if inconsistencies > 0:
                db.commit()
                logger.warning(f"🔧 修复了 {inconsistencies} 个状态不一致的计划")
            
            return inconsistencies
            
        except Exception as e:
            logger.error(f"❌ 修复状态不一致时出错: {e}")
            db.rollback()
            return 0
    
    def _fix_orphaned_plans(self, db: Session) -> int:
        """修复孤儿计划 - 长时间未被处理的计划"""
        try:
            # 查找孤儿计划：创建超过1小时但仍未被提审的计划
            orphaned_plans = db.execute(text("""
                UPDATE campaigns 
                SET appeal_status = 'appeal_pending',
                    appeal_error_message = 'Auto-recovered orphaned plan',
                    last_updated = NOW()
                WHERE status = 'AUDITING'
                  AND first_appeal_at IS NULL
                  AND created_at < NOW() - INTERVAL '1 hour'
                  AND (appeal_status IS NULL OR appeal_status NOT IN ('appeal_pending', 'appeal_executing'))
            """)).rowcount
            
            if orphaned_plans > 0:
                db.commit()
                logger.warning(f"🔧 修复了 {orphaned_plans} 个孤儿计划")
            
            return orphaned_plans
            
        except Exception as e:
            logger.error(f"❌ 修复孤儿计划时出错: {e}")
            db.rollback()
            return 0
    
    def _update_healing_stats(self, results: Dict[str, int]):
        """更新自愈统计"""
        self.healing_stats['silent_failures_fixed'] += results['silent_failures']
        self.healing_stats['stuck_plans_fixed'] += results['stuck_plans']
        self.healing_stats['status_inconsistencies_fixed'] += results['status_inconsistencies']
    
    def get_system_health_status(self, db: Session) -> Dict[str, any]:
        """获取系统健康状态"""
        try:
            # 统计各种状态的计划数量
            health_query = text("""
                SELECT
                    COUNT(*) as total_plans,
                    COUNT(CASE WHEN c.status = 'AUDITING' AND c.first_appeal_at IS NULL THEN 1 END) as pending_appeal,
                    COUNT(CASE WHEN c.status = 'AUDITING' AND c.first_appeal_at IS NOT NULL THEN 1 END) as appealed,
                    COUNT(CASE WHEN c.appeal_status = 'appeal_pending' THEN 1 END) as ready_for_appeal,
                    COUNT(CASE WHEN c.appeal_status IN ('appeal_executing', 'appealing') THEN 1 END) as executing,
                    COUNT(CASE WHEN c.status = 'MONITORING' THEN 1 END) as monitoring,
                    COUNT(CASE WHEN c.status = 'COMPLETED' THEN 1 END) as completed
                FROM campaigns c
                JOIN ad_accounts aa ON c.account_id = aa.id
                WHERE c.created_at >= NOW() - INTERVAL '24 hours'
            """)
            
            result = db.execute(health_query).fetchone()
            
            health_status = {
                'total_plans': result.total_plans,
                'pending_appeal': result.pending_appeal,
                'appealed': result.appealed,
                'ready_for_appeal': result.ready_for_appeal,
                'executing': result.executing,
                'monitoring': result.monitoring,
                'completed': result.completed,
                'appeal_success_rate': (result.appealed / result.total_plans * 100) if result.total_plans > 0 else 0,
                'completion_rate': (result.completed / result.total_plans * 100) if result.total_plans > 0 else 0,
                'system_health': 'HEALTHY' if result.pending_appeal == 0 else 'NEEDS_ATTENTION'
            }
            
            return health_status
            
        except Exception as e:
            logger.error(f"❌ 获取系统健康状态失败: {e}")
            return {'error': str(e)}
    
    def ensure_workflow_integrity(self, db: Session) -> bool:
        """确保工作流完整性 - 核心方法"""
        logger.info("🔍 检查工作流完整性...")
        
        try:
            # 1. 执行自愈
            healing_results = self.perform_self_healing(db)
            
            # 2. 检查系统健康
            health_status = self.get_system_health_status(db)
            
            # 3. 评估系统状态
            is_healthy = (
                health_status.get('system_health') == 'HEALTHY' and
                health_status.get('appeal_success_rate', 0) >= 95
            )
            
            if is_healthy:
                logger.success("✅ 工作流完整性检查通过")
            else:
                logger.warning(f"⚠️ 工作流需要关注: {health_status}")
            
            return is_healthy
            
        except Exception as e:
            logger.error(f"❌ 工作流完整性检查失败: {e}")
            return False


# 全局自愈实例
workflow_self_healing = WorkflowSelfHealing()


def integrate_self_healing_into_workflow(db: Session) -> bool:
    """将自愈机制集成到工作流中"""
    try:
        # 每次工作流执行前进行自愈检查
        return workflow_self_healing.ensure_workflow_integrity(db)
    except Exception as e:
        logger.error(f"❌ 工作流自愈集成失败: {e}")
        return False


def get_workflow_health_report() -> Dict[str, any]:
    """获取工作流健康报告"""
    try:
        with SessionLocal() as db:
            health_status = workflow_self_healing.get_system_health_status(db)
            health_status['healing_stats'] = workflow_self_healing.healing_stats
            return health_status
    except Exception as e:
        logger.error(f"❌ 获取健康报告失败: {e}")
        return {'error': str(e)}
