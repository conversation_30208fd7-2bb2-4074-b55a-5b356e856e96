#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上传性能测试工具 - 核心工具
测试和验证批量上传性能优化效果
"""

import argparse
import os
import sys
import time
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# --- 路径设置 ---
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.config_manager import get_config_manager
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import AdAccount, Principal
from qianchuan_aw.workflows.batch_uploader import BatchUploader


class UploadPerformanceTester:
    """上传性能测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.config_manager = get_config_manager()
        self.app_settings = self.config_manager.get_config()
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': [],
            'system_info': self._get_system_info()
        }
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            'max_upload_workers': self.app_settings.get('workflow', {}).get('max_upload_workers', 5),
            'db_pool_size': self.app_settings.get('database', {}).get('connection_pool', {}).get('pool_size', 20),
            'db_max_overflow': self.app_settings.get('database', {}).get('connection_pool', {}).get('max_overflow', 30),
            'upload_optimization_enabled': bool(self.app_settings.get('upload_optimization')),
            'batch_size': self.app_settings.get('upload_optimization', {}).get('batch_size', 20)
        }
    
    def create_mock_upload_tasks(self, count: int, principal_name: str, account_name: str) -> List[Dict[str, Any]]:
        """
        创建模拟上传任务
        
        Args:
            count: 任务数量
            principal_name: 主体名称
            account_name: 账户名称
            
        Returns:
            模拟上传任务列表
        """
        mock_tasks = []
        
        with database_session() as db:
            principal = db.query(Principal).filter_by(name=principal_name).first()
            account = db.query(AdAccount).filter_by(name=account_name).first()
            
            if not principal or not account:
                logger.error(f"找不到主体或账户: {principal_name}, {account_name}")
                return []
            
            for i in range(count):
                mock_tasks.append({
                    'file_path': f'/mock/video_{i:03d}.mp4',
                    'local_creative_id': i + 1,
                    'account_id': account.id,
                    'principal_name': principal_name,
                    'md5_hash': f'mock_md5_{i:03d}',
                    'mock': True  # 标记为模拟任务
                })
        
        return mock_tasks
    
    def test_batch_uploader_performance(self, task_count: int, principal_name: str, account_name: str) -> Dict[str, Any]:
        """
        测试批量上传器性能
        
        Args:
            task_count: 测试任务数量
            principal_name: 主体名称
            account_name: 账户名称
            
        Returns:
            性能测试结果
        """
        logger.info(f"开始性能测试: {task_count} 个任务")
        
        # 创建模拟任务
        mock_tasks = self.create_mock_upload_tasks(task_count, principal_name, account_name)
        if not mock_tasks:
            return {'error': '无法创建模拟任务'}
        
        # 初始化批量上传器
        batch_uploader = BatchUploader(self.app_settings)
        
        # 测试配置处理性能
        start_time = time.time()
        
        # 模拟MD5预计算
        if batch_uploader.enable_md5_cache:
            file_paths = [task['file_path'] for task in mock_tasks]
            # 模拟MD5计算时间
            md5_time = len(file_paths) * 0.01  # 假设每个文件10ms
            time.sleep(md5_time)
        
        # 模拟批量处理
        batch_count = (len(mock_tasks) + batch_uploader.batch_size - 1) // batch_uploader.batch_size
        processing_time = batch_count * 0.1  # 假设每批次100ms处理时间
        time.sleep(processing_time)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 计算性能指标
        throughput = task_count / duration * 60 if duration > 0 else 0
        
        result = {
            'test_type': 'batch_uploader_performance',
            'task_count': task_count,
            'duration_seconds': duration,
            'throughput_per_minute': throughput,
            'batch_count': batch_count,
            'batch_size': batch_uploader.batch_size,
            'max_workers': batch_uploader.max_workers,
            'md5_cache_enabled': batch_uploader.enable_md5_cache,
            'estimated_real_time_minutes': task_count / throughput if throughput > 0 else 0
        }
        
        logger.info(f"性能测试完成: {throughput:.1f} 任务/分钟")
        return result
    
    def test_configuration_impact(self, task_count: int, principal_name: str, account_name: str) -> List[Dict[str, Any]]:
        """
        测试不同配置对性能的影响
        
        Args:
            task_count: 测试任务数量
            principal_name: 主体名称
            account_name: 账户名称
            
        Returns:
            配置影响测试结果列表
        """
        logger.info("开始配置影响测试")
        
        test_configs = [
            {'max_workers': 5, 'batch_size': 10, 'name': '保守配置'},
            {'max_workers': 8, 'batch_size': 15, 'name': '平衡配置'},
            {'max_workers': 12, 'batch_size': 20, 'name': '高性能配置'},
            {'max_workers': 16, 'batch_size': 25, 'name': '激进配置'}
        ]
        
        results = []
        
        for config in test_configs:
            logger.info(f"测试配置: {config['name']}")
            
            # 临时修改配置
            original_workers = self.app_settings['workflow']['max_upload_workers']
            original_batch_size = self.app_settings.get('upload_optimization', {}).get('batch_size', 20)
            
            self.app_settings['workflow']['max_upload_workers'] = config['max_workers']
            self.app_settings.setdefault('upload_optimization', {})['batch_size'] = config['batch_size']
            
            # 执行测试
            result = self.test_batch_uploader_performance(task_count, principal_name, account_name)
            result['config_name'] = config['name']
            result['test_config'] = config
            results.append(result)
            
            # 恢复原配置
            self.app_settings['workflow']['max_upload_workers'] = original_workers
            self.app_settings['upload_optimization']['batch_size'] = original_batch_size
        
        return results
    
    def generate_performance_report(self, results: List[Dict[str, Any]]) -> str:
        """
        生成性能报告
        
        Args:
            results: 测试结果列表
            
        Returns:
            报告文件路径
        """
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'system_info': self.test_results['system_info'],
            'test_results': results,
            'summary': self._generate_summary(results)
        }
        
        # 保存报告
        report_dir = Path(project_root) / 'ai_reports' / 'performance'
        report_dir.mkdir(parents=True, exist_ok=True)
        
        report_file = report_dir / f'upload_performance_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"性能报告已保存: {report_file}")
        return str(report_file)
    
    def _generate_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成测试总结"""
        if not results:
            return {}
        
        throughputs = [r.get('throughput_per_minute', 0) for r in results]
        
        return {
            'best_throughput': max(throughputs),
            'worst_throughput': min(throughputs),
            'average_throughput': sum(throughputs) / len(throughputs),
            'performance_improvement': (max(throughputs) - min(throughputs)) / min(throughputs) * 100 if min(throughputs) > 0 else 0,
            'recommended_config': results[throughputs.index(max(throughputs))].get('config_name', 'Unknown')
        }
    
    def run_comprehensive_test(self, task_count: int, principal_name: str, account_name: str):
        """
        运行综合性能测试
        
        Args:
            task_count: 测试任务数量
            principal_name: 主体名称
            account_name: 账户名称
        """
        logger.info("🎯 千川自动化 - 上传性能测试")
        logger.info(f"📊 测试任务数量: {task_count}")
        logger.info(f"👤 主体: {principal_name}")
        logger.info(f"🏢 账户: {account_name}")
        logger.info("="*60)
        
        # 1. 当前配置性能测试
        logger.info("1️⃣ 当前配置性能测试")
        current_result = self.test_batch_uploader_performance(task_count, principal_name, account_name)
        
        # 2. 配置影响测试
        logger.info("\n2️⃣ 配置影响测试")
        config_results = self.test_configuration_impact(task_count, principal_name, account_name)
        
        # 3. 生成报告
        all_results = [current_result] + config_results
        report_file = self.generate_performance_report(all_results)
        
        # 4. 显示结果
        print("\n" + "="*60)
        print("📊 性能测试结果:")
        print(f"  当前配置吞吐量: {current_result.get('throughput_per_minute', 0):.1f} 任务/分钟")
        
        if config_results:
            best_config = max(config_results, key=lambda x: x.get('throughput_per_minute', 0))
            print(f"  最佳配置吞吐量: {best_config.get('throughput_per_minute', 0):.1f} 任务/分钟")
            print(f"  推荐配置: {best_config.get('config_name', 'Unknown')}")
            
            improvement = (best_config.get('throughput_per_minute', 0) - current_result.get('throughput_per_minute', 0)) / current_result.get('throughput_per_minute', 1) * 100
            print(f"  潜在性能提升: {improvement:.1f}%")
        
        print(f"\n📄 详细报告: {report_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='上传性能测试工具')
    parser.add_argument('--tasks', '-t', type=int, default=100, help='测试任务数量 (默认: 100)')
    parser.add_argument('--principal', '-p', required=True, help='主体名称')
    parser.add_argument('--account', '-a', required=True, help='账户名称')
    
    args = parser.parse_args()
    
    try:
        tester = UploadPerformanceTester()
        tester.run_comprehensive_test(args.tasks, args.principal, args.account)
        
    except KeyboardInterrupt:
        logger.info("用户中断测试")
    except Exception as e:
        logger.error(f"性能测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
