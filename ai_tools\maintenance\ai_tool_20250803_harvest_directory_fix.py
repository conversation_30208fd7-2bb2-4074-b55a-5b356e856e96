#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复收割目录路径配置不一致问题
清理条件: 功能被替代时删除
"""

import os
import sys
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

from loguru import logger

def test_harvest_directory_configuration():
    """测试收割目录配置"""
    logger.info("🔍 测试收割目录配置...")
    
    try:
        from qianchuan_aw.utils.config_loader import load_settings
        from qianchuan_aw.utils.workflow_status import WorkflowDirectories
        
        # 加载配置
        app_settings = load_settings()
        
        # 测试参数
        base_dir = "D:/workflow_assets"
        principal_name = "缇萃百货"
        filename = "test_video.mp4"
        
        # 测试新的get_harvest_path方法
        harvest_path_with_config = WorkflowDirectories.get_harvest_path(
            base_dir, principal_name, filename, app_settings
        )
        
        # 测试不传配置的情况
        harvest_path_without_config = WorkflowDirectories.get_harvest_path(
            base_dir, principal_name, filename
        )
        
        logger.info("📊 收割目录配置测试结果:")
        logger.info(f"   使用配置文件: {harvest_path_with_config}")
        logger.info(f"   使用默认值: {harvest_path_without_config}")
        
        # 检查配置文件中的设置
        workflow_dirs = app_settings.get('workflow', {}).get('workflow_dirs', {})
        dir_03_setting = workflow_dirs.get('DIR_03_MATERIALS_APPROVED')
        
        logger.info(f"   配置文件中DIR_03_MATERIALS_APPROVED: {dir_03_setting}")
        
        # 验证是否使用了正确的目录
        if "03_materials_approved" in harvest_path_with_config:
            logger.success("✅ 修复成功: 现在使用配置文件中的03_materials_approved目录")
            return True
        elif "03_harvested_materials" in harvest_path_with_config:
            logger.warning("⚠️ 仍在使用硬编码的03_harvested_materials目录")
            return False
        else:
            logger.error("❌ 未知的目录配置")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试收割目录配置失败: {e}")
        return False

def test_workflow_file_manager():
    """测试工作流文件管理器"""
    logger.info("🔍 测试工作流文件管理器...")
    
    try:
        from qianchuan_aw.utils.config_loader import load_settings
        from qianchuan_aw.utils.workflow_file_operations import get_workflow_file_manager
        
        # 加载配置
        app_settings = load_settings()
        
        # 获取文件管理器
        file_manager = get_workflow_file_manager(app_settings)
        
        logger.info("✅ 工作流文件管理器创建成功")
        logger.info(f"   基础目录: {file_manager.base_dir}")
        logger.info(f"   配置已加载: {file_manager.app_settings is not None}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试工作流文件管理器失败: {e}")
        return False

def verify_directory_exists():
    """验证目录是否存在"""
    logger.info("🔍 验证收割目录是否存在...")
    
    try:
        # 检查用户期望的目录
        expected_dir = "D:/workflow_assets/03_materials_approved/缇萃百货"
        
        if os.path.exists(expected_dir):
            logger.success(f"✅ 用户期望的目录存在: {expected_dir}")
            
            # 列出目录内容
            try:
                files = os.listdir(expected_dir)
                logger.info(f"   目录中有 {len(files)} 个文件/文件夹")
                if files:
                    logger.info(f"   最近的几个项目: {files[:5]}")
            except Exception as e:
                logger.warning(f"   无法列出目录内容: {e}")
                
            return True
        else:
            logger.warning(f"⚠️ 用户期望的目录不存在: {expected_dir}")
            
            # 检查是否有新目录
            new_dir = "D:/workflow_assets/03_harvested_materials/缇萃百货"
            if os.path.exists(new_dir):
                logger.info(f"   发现新目录: {new_dir}")
                try:
                    files = os.listdir(new_dir)
                    logger.info(f"   新目录中有 {len(files)} 个文件/文件夹")
                except Exception as e:
                    logger.warning(f"   无法列出新目录内容: {e}")
            
            return False
            
    except Exception as e:
        logger.error(f"❌ 验证目录失败: {e}")
        return False

def create_migration_plan():
    """创建迁移计划"""
    logger.info("📋 创建目录迁移计划...")
    
    old_dir = "D:/workflow_assets/03_harvested_materials"
    new_dir = "D:/workflow_assets/03_materials_approved"
    
    migration_plan = {
        'issue': '收割目录路径不一致',
        'problem': '代码使用03_harvested_materials，用户期望03_materials_approved',
        'solution': '修改代码使用配置文件中的目录设置',
        'migration_needed': False,
        'actions': []
    }
    
    try:
        # 检查是否需要迁移
        if os.path.exists(old_dir) and not os.path.exists(new_dir):
            migration_plan['migration_needed'] = True
            migration_plan['actions'].append(f"重命名目录: {old_dir} -> {new_dir}")
        elif os.path.exists(old_dir) and os.path.exists(new_dir):
            migration_plan['migration_needed'] = True
            migration_plan['actions'].append(f"合并目录: {old_dir} -> {new_dir}")
            migration_plan['actions'].append(f"删除旧目录: {old_dir}")
        
        logger.info("📊 迁移计划:")
        logger.info(f"   问题: {migration_plan['issue']}")
        logger.info(f"   需要迁移: {migration_plan['migration_needed']}")
        
        if migration_plan['actions']:
            logger.info("   建议操作:")
            for action in migration_plan['actions']:
                logger.info(f"     - {action}")
        
        return migration_plan
        
    except Exception as e:
        logger.error(f"❌ 创建迁移计划失败: {e}")
        return migration_plan

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🔧 收割目录路径配置修复工具")
    logger.info("=" * 80)
    
    success_count = 0
    total_tests = 4
    
    try:
        # 测试1: 收割目录配置
        if test_harvest_directory_configuration():
            success_count += 1
        
        # 测试2: 工作流文件管理器
        if test_workflow_file_manager():
            success_count += 1
        
        # 测试3: 验证目录存在
        if verify_directory_exists():
            success_count += 1
        
        # 测试4: 创建迁移计划
        migration_plan = create_migration_plan()
        if migration_plan:
            success_count += 1
        
        logger.info("=" * 80)
        logger.info("📊 修复总结:")
        logger.info("=" * 80)
        
        logger.info(f"✅ 成功测试: {success_count}/{total_tests} 项")
        
        if success_count >= 3:
            logger.success("🎉 收割目录配置修复完成！")
            logger.info("\n💡 修复说明:")
            logger.info("   1. 代码现在会优先使用配置文件中的目录设置")
            logger.info("   2. 配置文件中设置为: 03_materials_approved")
            logger.info("   3. 新收割的素材将放入您期望的目录")
            
            if migration_plan.get('migration_needed'):
                logger.info("\n⚠️ 注意:")
                logger.info("   发现可能需要目录迁移，请手动处理现有文件")
        else:
            logger.warning(f"⚠️ 部分测试失败，请检查配置")
        
        return success_count >= 3
        
    except Exception as e:
        logger.error(f"❌ 修复过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
