#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化系统数据一致性验证工具
清理条件: 功能被替代时删除
"""

import os
import sys
from datetime import datetime, date
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

from loguru import logger
from sqlalchemy import create_engine, text
from qianchuan_aw.utils.config_loader import load_settings

class DataConsistencyVerifier:
    """数据一致性验证器"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.db_engine = self._create_db_engine()
        self.verification_date = '2025-08-02'
        self.test_account_ids = []
        self.report_data = {
            'verification_date': self.verification_date,
            'timestamp': datetime.now().isoformat(),
            'test_accounts': [],
            'video_upload_stats': {},
            'campaign_creation_stats': {},
            'appeal_operation_stats': {},
            'material_audit_stats': {},
            'consistency_issues': [],
            'file_system_verification': {},
            'recommendations': []
        }
    
    def _create_db_engine(self):
        """创建数据库引擎"""
        db_config = self.app_settings.get('database', {}).get('postgresql', {})
        db_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['dbname']}"
        return create_engine(db_url)
    
    def identify_test_accounts(self):
        """识别测试账户"""
        logger.info("🔍 识别测试账户...")
        
        query = """
        SELECT id, name, account_id_qc, status, created_at
        FROM ad_accounts 
        WHERE name ILIKE '%测试%' OR name ILIKE '%test%'
        ORDER BY created_at DESC
        """
        
        with self.db_engine.connect() as conn:
            result = conn.execute(text(query))
            accounts = result.fetchall()
            
            for account in accounts:
                account_info = {
                    'id': account.id,
                    'name': account.name,
                    'account_id_qc': account.account_id_qc,
                    'status': account.status,
                    'created_at': account.created_at.isoformat()
                }
                self.report_data['test_accounts'].append(account_info)
                self.test_account_ids.append(account.id)
                
                logger.info(f"   测试账户: {account.name} (ID: {account.id})")
        
        logger.success(f"✅ 识别到 {len(self.test_account_ids)} 个测试账户")
        return self.test_account_ids
    
    def verify_video_upload_stats(self):
        """验证视频上传统计"""
        logger.info("📊 验证视频上传统计...")
        
        query = """
        WITH test_accounts AS (
            SELECT id FROM ad_accounts 
            WHERE name ILIKE '%测试%' OR name ILIKE '%test%'
        )
        SELECT 
            lc.status,
            COUNT(*) as count,
            COUNT(CASE WHEN lc.material_id_qc IS NOT NULL THEN 1 END) as with_material_id,
            COUNT(CASE WHEN lc.video_id IS NOT NULL THEN 1 END) as with_video_id,
            MIN(lc.created_at) as first_upload,
            MAX(lc.created_at) as last_upload
        FROM local_creatives lc
        JOIN test_accounts ta ON lc.uploaded_to_account_id = ta.id
        WHERE DATE(lc.created_at) = :verification_date
        GROUP BY lc.status
        ORDER BY count DESC
        """
        
        with self.db_engine.connect() as conn:
            result = conn.execute(text(query), {'verification_date': self.verification_date})
            stats = result.fetchall()
            
            total_uploads = sum(row.count for row in stats)
            
            upload_stats = {
                'total_attempts': total_uploads,
                'by_status': {},
                'success_rate': 0
            }
            
            for row in stats:
                status_info = {
                    'count': row.count,
                    'with_material_id': row.with_material_id,
                    'with_video_id': row.with_video_id,
                    'percentage': round(row.count * 100.0 / total_uploads, 2),
                    'first_upload': row.first_upload.isoformat() if row.first_upload else None,
                    'last_upload': row.last_upload.isoformat() if row.last_upload else None
                }
                upload_stats['by_status'][row.status] = status_info
                
                if row.status in ['approved', 'testing_pending_review']:
                    upload_stats['success_rate'] += status_info['percentage']
                
                logger.info(f"   {row.status}: {row.count} 个 ({status_info['percentage']}%)")
        
        self.report_data['video_upload_stats'] = upload_stats
        logger.success(f"✅ 视频上传统计完成: 总计 {total_uploads} 个上传尝试")
        return upload_stats
    
    def verify_campaign_creation_stats(self):
        """验证计划创建统计"""
        logger.info("📊 验证计划创建统计...")
        
        query = """
        WITH test_accounts AS (
            SELECT id FROM ad_accounts 
            WHERE name ILIKE '%测试%' OR name ILIKE '%test%'
        )
        SELECT 
            c.status,
            c.appeal_status,
            COUNT(*) as count,
            COUNT(CASE WHEN c.campaign_id_qc IS NOT NULL THEN 1 END) as with_campaign_id,
            MIN(c.created_at) as first_created,
            MAX(c.created_at) as last_created
        FROM campaigns c
        JOIN test_accounts ta ON c.account_id = ta.id
        WHERE DATE(c.created_at) = :verification_date
        GROUP BY c.status, c.appeal_status
        ORDER BY count DESC
        """

        with self.db_engine.connect() as conn:
            result = conn.execute(text(query), {'verification_date': self.verification_date})
            stats = result.fetchall()
            
            total_campaigns = sum(row.count for row in stats)
            
            campaign_stats = {
                'total_created': total_campaigns,
                'by_status': {},
                'creation_success_rate': 0
            }
            
            for row in stats:
                key = f"{row.status}_{row.appeal_status or 'none'}"
                status_info = {
                    'status': row.status,
                    'appeal_status': row.appeal_status,
                    'count': row.count,
                    'with_campaign_id': row.with_campaign_id,
                    'percentage': round(row.count * 100.0 / total_campaigns, 2),
                    'first_created': row.first_created.isoformat() if row.first_created else None,
                    'last_created': row.last_created.isoformat() if row.last_created else None
                }
                campaign_stats['by_status'][key] = status_info
                
                # 计算创建成功率（有campaign_id_qc的）
                if row.with_campaign_id > 0:
                    campaign_stats['creation_success_rate'] += status_info['percentage']
                
                logger.info(f"   {row.status}/{row.appeal_status}: {row.count} 个 ({status_info['percentage']}%)")
        
        self.report_data['campaign_creation_stats'] = campaign_stats
        logger.success(f"✅ 计划创建统计完成: 总计 {total_campaigns} 个计划")
        return campaign_stats
    
    def verify_appeal_operations(self):
        """验证提审操作统计"""
        logger.info("📊 验证提审操作统计...")
        
        query = """
        WITH test_accounts AS (
            SELECT id FROM ad_accounts 
            WHERE name ILIKE '%测试%' OR name ILIKE '%test%'
        )
        SELECT 
            CASE 
                WHEN c.first_appeal_at IS NOT NULL THEN '已提审'
                ELSE '未提审'
            END as appeal_action,
            c.appeal_status,
            c.appeal_result,
            COUNT(*) as count,
            COUNT(CASE WHEN c.appeal_completed_at IS NOT NULL THEN 1 END) as completed_appeals,
            AVG(c.appeal_attempt_count) as avg_attempts
        FROM campaigns c
        JOIN test_accounts ta ON c.account_id = ta.id
        WHERE DATE(c.created_at) = :verification_date
        GROUP BY
            CASE
                WHEN c.first_appeal_at IS NOT NULL THEN '已提审'
                ELSE '未提审'
            END,
            c.appeal_status,
            c.appeal_result
        ORDER BY count DESC
        """

        with self.db_engine.connect() as conn:
            result = conn.execute(text(query), {'verification_date': self.verification_date})
            stats = result.fetchall()
            
            total_campaigns = sum(row.count for row in stats)
            
            appeal_stats = {
                'total_campaigns': total_campaigns,
                'by_action': {},
                'appeal_rate': 0,
                'completion_rate': 0
            }
            
            for row in stats:
                key = f"{row.appeal_action}_{row.appeal_status or 'none'}_{row.appeal_result or 'none'}"
                action_info = {
                    'appeal_action': row.appeal_action,
                    'appeal_status': row.appeal_status,
                    'appeal_result': row.appeal_result,
                    'count': row.count,
                    'completed_appeals': row.completed_appeals,
                    'avg_attempts': round(float(row.avg_attempts), 2) if row.avg_attempts else 0,
                    'percentage': round(row.count * 100.0 / total_campaigns, 2)
                }
                appeal_stats['by_action'][key] = action_info
                
                if row.appeal_action == '已提审':
                    appeal_stats['appeal_rate'] += action_info['percentage']
                
                if row.completed_appeals > 0:
                    appeal_stats['completion_rate'] += round(row.completed_appeals * 100.0 / total_campaigns, 2)
                
                logger.info(f"   {row.appeal_action}/{row.appeal_status}: {row.count} 个 ({action_info['percentage']}%)")
        
        self.report_data['appeal_operation_stats'] = appeal_stats
        logger.success(f"✅ 提审操作统计完成: 提审率 {appeal_stats['appeal_rate']}%")
        return appeal_stats

    def verify_material_audit_stats(self):
        """验证素材审核和收割统计"""
        logger.info("📊 验证素材审核和收割统计...")

        query = """
        WITH test_accounts AS (
            SELECT id FROM ad_accounts
            WHERE name ILIKE '%测试%' OR name ILIKE '%test%'
        )
        SELECT
            pc.review_status,
            lc.status as local_status,
            lc.harvest_status,
            COUNT(*) as count,
            COUNT(CASE WHEN pc.last_checked >= :verification_date THEN 1 END) as checked_on_date,
            MAX(pc.last_checked) as last_review_check,
            MAX(lc.last_harvest_attempt) as last_harvest_attempt
        FROM platform_creatives pc
        JOIN local_creatives lc ON pc.local_creative_id = lc.id
        JOIN test_accounts ta ON pc.account_id = ta.id
        WHERE DATE(lc.created_at) = :verification_date
        GROUP BY pc.review_status, lc.status, lc.harvest_status
        ORDER BY count DESC
        """

        with self.db_engine.connect() as conn:
            result = conn.execute(text(query), {'verification_date': self.verification_date})
            stats = result.fetchall()

            total_materials = sum(row.count for row in stats)

            audit_stats = {
                'total_materials': total_materials,
                'by_status': {},
                'audit_pass_rate': 0,
                'harvest_rate': 0
            }

            for row in stats:
                key = f"{row.review_status}_{row.local_status}_{row.harvest_status}"
                status_info = {
                    'review_status': row.review_status,
                    'local_status': row.local_status,
                    'harvest_status': row.harvest_status,
                    'count': row.count,
                    'checked_on_date': row.checked_on_date,
                    'percentage': round(row.count * 100.0 / total_materials, 2),
                    'last_review_check': row.last_review_check.isoformat() if row.last_review_check else None,
                    'last_harvest_attempt': row.last_harvest_attempt.isoformat() if row.last_harvest_attempt else None
                }
                audit_stats['by_status'][key] = status_info

                if row.review_status == 'PASS':
                    audit_stats['audit_pass_rate'] += status_info['percentage']

                if row.harvest_status == 'harvested':
                    audit_stats['harvest_rate'] += status_info['percentage']

                logger.info(f"   {row.review_status}/{row.local_status}/{row.harvest_status}: {row.count} 个")

        self.report_data['material_audit_stats'] = audit_stats
        logger.success(f"✅ 素材审核统计完成: 总计 {total_materials} 个素材")
        return audit_stats

    def detect_consistency_issues(self):
        """检测数据一致性问题"""
        logger.info("🔍 检测数据一致性问题...")

        query = """
        WITH test_accounts AS (
            SELECT id, name FROM ad_accounts
            WHERE name ILIKE '%测试%' OR name ILIKE '%test%'
        ),
        inconsistency_analysis AS (
            SELECT
                lc.id as local_creative_id,
                ta.name as account_name,
                lc.filename,
                lc.material_id_qc as local_material_id,
                pc.material_id_qc as platform_material_id,
                lc.status as local_status,
                pc.review_status as platform_status,
                lc.harvest_status,
                pc.last_checked,
                CASE
                    WHEN lc.material_id_qc != pc.material_id_qc THEN 'material_id_mismatch'
                    WHEN lc.status = 'approved' AND pc.review_status NOT IN ('PASS', 'APPROVED') THEN 'local_approved_platform_not_pass'
                    WHEN lc.status = 'rejected' AND pc.review_status = 'PASS' THEN 'local_rejected_platform_pass'
                    WHEN lc.harvest_status = 'not_harvested' AND lc.status = 'approved' AND pc.review_status = 'PASS' THEN 'harvest_pending'
                    WHEN pc.last_checked IS NULL THEN 'never_checked'
                    ELSE 'consistent'
                END as issue_type
            FROM local_creatives lc
            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
            JOIN test_accounts ta ON lc.uploaded_to_account_id = ta.id
            WHERE DATE(lc.created_at) = :verification_date
        )
        SELECT
            issue_type,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage,
            STRING_AGG(local_creative_id::text, ',' ORDER BY local_creative_id) as sample_ids_str
        FROM (
            SELECT DISTINCT issue_type, local_creative_id,
                   ROW_NUMBER() OVER (PARTITION BY issue_type ORDER BY local_creative_id) as rn
            FROM inconsistency_analysis
        ) ranked
        WHERE rn <= 5
        GROUP BY issue_type
        ORDER BY count DESC
        """

        with self.db_engine.connect() as conn:
            result = conn.execute(text(query), {'verification_date': self.verification_date})
            issues = result.fetchall()

            consistency_issues = []
            total_records = sum(row.count for row in issues)

            for row in issues:
                sample_ids = row.sample_ids_str.split(',') if row.sample_ids_str else []
                issue_info = {
                    'issue_type': row.issue_type,
                    'count': row.count,
                    'percentage': row.percentage,
                    'sample_ids': [int(id_str) for id_str in sample_ids if id_str.strip()],
                    'severity': self._assess_issue_severity(row.issue_type, row.percentage)
                }
                consistency_issues.append(issue_info)

                severity_icon = "🔴" if issue_info['severity'] == 'high' else "🟡" if issue_info['severity'] == 'medium' else "🟢"
                logger.info(f"   {severity_icon} {row.issue_type}: {row.count} 个 ({row.percentage}%)")

        self.report_data['consistency_issues'] = consistency_issues
        logger.success(f"✅ 一致性检测完成: 发现 {len([i for i in consistency_issues if i['issue_type'] != 'consistent'])} 类问题")
        return consistency_issues

    def _assess_issue_severity(self, issue_type, percentage):
        """评估问题严重程度"""
        if issue_type == 'consistent':
            return 'none'
        elif issue_type in ['material_id_mismatch', 'local_rejected_platform_pass']:
            return 'high'
        elif issue_type in ['local_approved_platform_not_pass', 'never_checked']:
            return 'medium' if percentage > 10 else 'low'
        elif issue_type == 'harvest_pending':
            return 'low'
        else:
            return 'medium'

    def verify_file_system(self):
        """验证文件系统状态"""
        logger.info("📁 验证文件系统状态...")

        # 检查工作流目录
        workflow_base = self.app_settings.get('custom_workflow_assets_dir', 'D:/workflow_assets')

        directories_to_check = [
            '01_materials_to_process/缇萃百货',
            '03_materials_approved/缇萃百货',
            '00_uploaded_archive'
        ]

        file_system_stats = {
            'workflow_base_dir': workflow_base,
            'directories': {},
            'total_files_found': 0
        }

        for dir_path in directories_to_check:
            full_path = os.path.join(workflow_base, dir_path)
            dir_info = {
                'path': full_path,
                'exists': os.path.exists(full_path),
                'file_count': 0,
                'subdirs': []
            }

            if dir_info['exists']:
                try:
                    # 统计文件数量
                    for root, dirs, files in os.walk(full_path):
                        dir_info['file_count'] += len(files)
                        if root != full_path:
                            rel_path = os.path.relpath(root, full_path)
                            dir_info['subdirs'].append({
                                'path': rel_path,
                                'file_count': len(files)
                            })

                    file_system_stats['total_files_found'] += dir_info['file_count']
                    logger.info(f"   ✅ {dir_path}: {dir_info['file_count']} 个文件")
                except Exception as e:
                    logger.warning(f"   ⚠️ {dir_path}: 无法访问 - {e}")
                    dir_info['error'] = str(e)
            else:
                logger.warning(f"   ❌ {dir_path}: 目录不存在")

            file_system_stats['directories'][dir_path] = dir_info

        self.report_data['file_system_verification'] = file_system_stats
        logger.success(f"✅ 文件系统验证完成: 总计发现 {file_system_stats['total_files_found']} 个文件")
        return file_system_stats

    def generate_recommendations(self):
        """生成改进建议"""
        logger.info("💡 生成改进建议...")

        recommendations = []

        # 基于一致性问题生成建议
        for issue in self.report_data['consistency_issues']:
            if issue['issue_type'] == 'local_approved_platform_not_pass' and issue['percentage'] > 10:
                recommendations.append({
                    'priority': 'high',
                    'category': 'data_sync',
                    'issue': '本地状态与平台状态不同步',
                    'description': f"发现 {issue['count']} 个素材本地标记为approved但平台状态不是PASS",
                    'recommendation': '建议增加定期状态同步机制，确保本地数据库与千川API状态一致',
                    'action': '实现自动状态同步任务，每小时检查并更新状态不一致的记录'
                })

            if issue['issue_type'] == 'never_checked' and issue['percentage'] > 5:
                recommendations.append({
                    'priority': 'medium',
                    'category': 'monitoring',
                    'issue': '素材审核状态未检查',
                    'description': f"发现 {issue['count']} 个素材从未检查过审核状态",
                    'recommendation': '建议加强素材审核状态监控，确保所有上传的素材都能及时获得审核结果',
                    'action': '优化素材监控工作流，减少检查间隔时间'
                })

            if issue['issue_type'] == 'harvest_pending' and issue['count'] > 0:
                recommendations.append({
                    'priority': 'medium',
                    'category': 'harvest',
                    'issue': '收割操作滞后',
                    'description': f"发现 {issue['count']} 个已通过审核的素材未及时收割",
                    'recommendation': '建议优化收割工作流，确保通过审核的素材能够及时收割到目标目录',
                    'action': '检查收割工作流的触发条件和执行频率'
                })

        # 基于上传成功率生成建议
        upload_stats = self.report_data.get('video_upload_stats', {})
        if upload_stats.get('success_rate', 0) < 80:
            recommendations.append({
                'priority': 'high',
                'category': 'upload_quality',
                'issue': '视频上传成功率偏低',
                'description': f"视频上传成功率仅为 {upload_stats.get('success_rate', 0)}%",
                'recommendation': '建议分析上传失败原因，优化视频质量检查和上传流程',
                'action': '增加视频预处理步骤，提高上传前的质量验证'
            })

        # 基于提审完成率生成建议
        appeal_stats = self.report_data.get('appeal_operation_stats', {})
        if appeal_stats.get('completion_rate', 0) < 50:
            recommendations.append({
                'priority': 'medium',
                'category': 'appeal_process',
                'issue': '提审完成率偏低',
                'description': f"提审完成率仅为 {appeal_stats.get('completion_rate', 0)}%",
                'recommendation': '建议优化提审流程，增加提审结果的跟踪和处理机制',
                'action': '实现提审结果自动查询和状态更新功能'
            })

        self.report_data['recommendations'] = recommendations

        for rec in recommendations:
            priority_icon = "🔴" if rec['priority'] == 'high' else "🟡" if rec['priority'] == 'medium' else "🟢"
            logger.info(f"   {priority_icon} [{rec['category']}] {rec['issue']}")

        logger.success(f"✅ 生成了 {len(recommendations)} 条改进建议")
        return recommendations

    def generate_report(self):
        """生成详细报告"""
        logger.info("📋 生成数据一致性验证报告...")

        report_filename = f"ai_reports/audit/ai_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_data_consistency_verification.md"

        # 确保目录存在
        os.makedirs(os.path.dirname(report_filename), exist_ok=True)

        report_content = self._generate_markdown_report()

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_content)

        # 同时生成JSON格式的详细数据
        json_filename = report_filename.replace('.md', '.json')
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.report_data, f, ensure_ascii=False, indent=2, default=str)

        logger.success(f"✅ 报告已生成:")
        logger.info(f"   📄 Markdown报告: {report_filename}")
        logger.info(f"   📊 JSON数据: {json_filename}")

        return report_filename, json_filename

    def _generate_markdown_report(self):
        """生成Markdown格式报告"""
        content = f"""# 千川自动化系统数据一致性验证报告

**验证日期**: {self.report_data['verification_date']}
**生成时间**: {self.report_data['timestamp']}
**验证范围**: 测试账户数据

---

## 📊 执行摘要

### 测试账户概览
- **测试账户数量**: {len(self.report_data['test_accounts'])}
- **验证数据日期**: {self.verification_date}

### 关键指标
- **视频上传总数**: {self.report_data['video_upload_stats'].get('total_attempts', 0)}
- **计划创建总数**: {self.report_data['campaign_creation_stats'].get('total_created', 0)}
- **素材审核总数**: {self.report_data['material_audit_stats'].get('total_materials', 0)}
- **数据一致性**: {len([i for i in self.report_data['consistency_issues'] if i['issue_type'] == 'consistent'])} / {sum(i['count'] for i in self.report_data['consistency_issues'])} 记录一致

---

## 🎯 详细验证结果

### 1. 视频上传统计
"""

        # 添加视频上传统计
        upload_stats = self.report_data['video_upload_stats']
        content += f"""
**总上传尝试**: {upload_stats.get('total_attempts', 0)} 个
**成功率**: {upload_stats.get('success_rate', 0)}%

| 状态 | 数量 | 百分比 | 有素材ID | 有视频ID |
|------|------|--------|----------|----------|
"""

        for status, info in upload_stats.get('by_status', {}).items():
            content += f"| {status} | {info['count']} | {info['percentage']}% | {info['with_material_id']} | {info['with_video_id']} |\n"

        # 添加其他统计信息...
        content += f"""

### 2. 计划创建统计
**总创建数量**: {self.report_data['campaign_creation_stats'].get('total_created', 0)} 个
**创建成功率**: {self.report_data['campaign_creation_stats'].get('creation_success_rate', 0)}%

### 3. 数据一致性问题
"""

        for issue in self.report_data['consistency_issues']:
            severity_icon = "🔴" if issue['severity'] == 'high' else "🟡" if issue['severity'] == 'medium' else "🟢"
            content += f"- {severity_icon} **{issue['issue_type']}**: {issue['count']} 个 ({issue['percentage']}%)\n"

        content += f"""

### 4. 改进建议
"""

        for rec in self.report_data['recommendations']:
            priority_icon = "🔴" if rec['priority'] == 'high' else "🟡" if rec['priority'] == 'medium' else "🟢"
            content += f"""
#### {priority_icon} {rec['issue']}
- **类别**: {rec['category']}
- **描述**: {rec['description']}
- **建议**: {rec['recommendation']}
- **行动**: {rec['action']}
"""

        content += f"""

---

## 📈 系统稳定性评估

基于本次验证结果，千川自动化系统的整体稳定性评估：

- **数据完整性**: {'良好' if len([i for i in self.report_data['consistency_issues'] if i['severity'] == 'high']) == 0 else '需要改进'}
- **工作流效率**: {'正常' if upload_stats.get('success_rate', 0) > 70 else '需要优化'}
- **监控覆盖**: {'充分' if len([i for i in self.report_data['consistency_issues'] if i['issue_type'] == 'never_checked']) == 0 else '不足'}

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return content

    def run_full_verification(self):
        """运行完整验证流程"""
        logger.info("=" * 80)
        logger.info("🔍 千川自动化系统数据一致性验证")
        logger.info("=" * 80)

        try:
            # 1. 识别测试账户
            self.identify_test_accounts()

            # 2. 验证各项统计
            self.verify_video_upload_stats()
            self.verify_campaign_creation_stats()
            self.verify_appeal_operations()
            self.verify_material_audit_stats()

            # 3. 检测一致性问题
            self.detect_consistency_issues()

            # 4. 验证文件系统
            self.verify_file_system()

            # 5. 生成建议
            self.generate_recommendations()

            # 6. 生成报告
            report_file, json_file = self.generate_report()

            logger.info("=" * 80)
            logger.info("📊 验证总结")
            logger.info("=" * 80)

            # 显示关键指标
            upload_stats = self.report_data['video_upload_stats']
            campaign_stats = self.report_data['campaign_creation_stats']
            consistency_issues = self.report_data['consistency_issues']

            logger.info(f"✅ 测试账户: {len(self.test_account_ids)} 个")
            logger.info(f"📊 视频上传: {upload_stats.get('total_attempts', 0)} 个 (成功率: {upload_stats.get('success_rate', 0)}%)")
            logger.info(f"📊 计划创建: {campaign_stats.get('total_created', 0)} 个 (成功率: {campaign_stats.get('creation_success_rate', 0)}%)")

            consistent_count = sum(i['count'] for i in consistency_issues if i['issue_type'] == 'consistent')
            total_count = sum(i['count'] for i in consistency_issues)
            consistency_rate = round(consistent_count * 100.0 / total_count, 2) if total_count > 0 else 0

            logger.info(f"🎯 数据一致性: {consistency_rate}% ({consistent_count}/{total_count})")

            high_priority_issues = len([r for r in self.report_data['recommendations'] if r['priority'] == 'high'])
            if high_priority_issues > 0:
                logger.warning(f"⚠️ 发现 {high_priority_issues} 个高优先级问题需要处理")
            else:
                logger.success("✅ 未发现高优先级问题")

            logger.success(f"🎉 验证完成! 报告已保存至: {report_file}")

            return True

        except Exception as e:
            logger.error(f"❌ 验证过程中发生错误: {e}")
            return False

def main():
    """主函数"""
    verifier = DataConsistencyVerifier()
    success = verifier.run_full_verification()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
