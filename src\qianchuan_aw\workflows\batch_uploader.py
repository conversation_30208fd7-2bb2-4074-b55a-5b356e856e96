#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量上传器 - 核心工作流组件
优化大规模视频上传性能，支持并发处理和智能缓存
"""

import os
import hashlib
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
from pathlib import Path

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, AdAccount, Principal, PlatformCreative
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.utils.workflow_helpers import extract_cover_id_from_url


class BatchUploader:
    """批量上传器 - 高性能视频上传核心组件"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        """
        初始化批量上传器
        
        Args:
            app_settings: 应用配置
        """
        self.app_settings = app_settings
        self.upload_config = app_settings.get('upload_optimization', {})
        self.batch_size = self.upload_config.get('batch_size', 10)
        self.max_workers = app_settings.get('workflow', {}).get('max_upload_workers', 6)

        # 新增配置项
        self.upload_timeout = self.upload_config.get('upload_timeout', 120)
        self.batch_delay = self.upload_config.get('batch_delay_seconds', 2)
        self.library_query_delay = self.upload_config.get('library_query_delay', 5)
        self.library_query_retries = self.upload_config.get('library_query_retries', 3)
        
        # 性能优化配置
        self.enable_md5_cache = self.upload_config.get('pre_compute_md5', True)
        self.enable_validation_cache = self.upload_config.get('validation_cache_enabled', True)
        self.enable_batch_db = self.upload_config.get('batch_db_operations', True)
        
        # 缓存
        self.md5_cache = {}
        self.validation_cache = {}
        
        logger.info(f"批量上传器初始化完成 - 并发数: {self.max_workers}, 批次大小: {self.batch_size}")

    def compute_file_md5(self, file_path: str) -> Optional[str]:
        """
        计算文件MD5值，支持缓存
        
        Args:
            file_path: 文件路径
            
        Returns:
            MD5哈希值或None
        """
        if self.enable_md5_cache and file_path in self.md5_cache:
            return self.md5_cache[file_path]
        
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_md5.update(chunk)
            
            md5_hash = hash_md5.hexdigest()
            
            if self.enable_md5_cache:
                self.md5_cache[file_path] = md5_hash
            
            return md5_hash
            
        except Exception as e:
            logger.error(f"计算MD5失败: {file_path} - {e}")
            return None

    def batch_compute_md5(self, file_paths: List[str]) -> Dict[str, str]:
        """
        批量并行计算MD5值
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            文件路径到MD5的映射
        """
        if not self.enable_md5_cache:
            return {}
        
        md5_results = {}
        
        def compute_single_md5(file_path: str) -> tuple:
            md5_hash = self.compute_file_md5(file_path)
            return file_path, md5_hash
        
        # 并行计算MD5
        with ThreadPoolExecutor(max_workers=min(len(file_paths), 8)) as executor:
            futures = [executor.submit(compute_single_md5, fp) for fp in file_paths]
            
            for future in as_completed(futures):
                file_path, md5_hash = future.result()
                if md5_hash:
                    md5_results[file_path] = md5_hash
        
        logger.info(f"批量MD5计算完成: {len(md5_results)}/{len(file_paths)} 个文件")
        return md5_results

    def upload_single_video(self, upload_task: Dict[str, Any]) -> Dict[str, Any]:
        """
        上传单个视频 - 优化版本

        Args:
            upload_task: 上传任务信息

        Returns:
            上传结果
        """
        # 验证必需的参数
        required_fields = ['file_path', 'local_creative_id', 'account_id', 'principal_name']
        for field in required_fields:
            if field not in upload_task:
                return {
                    'success': False,
                    'file_path': upload_task.get('file_path', 'unknown'),
                    'error': f'缺少必需参数: {field}',
                    'error_type': 'parameter_error'
                }

        file_path = upload_task['file_path']
        local_creative_id = upload_task['local_creative_id']
        account_id = upload_task['account_id']
        principal_name = upload_task['principal_name']
        md5_hash = upload_task.get('md5_hash')
        
        try:
            # 第一阶段：快速数据库检查（短连接）
            principal_id = None
            account_id_qc = None

            with database_session() as db:
                local_creative = db.get(LocalCreative, local_creative_id)
                account = db.get(AdAccount, account_id)
                principal = db.query(Principal).filter_by(name=principal_name).first()

                if not all([local_creative, account, principal]):
                    return {
                        'success': False,
                        'file_path': file_path,
                        'error': '数据库记录不完整',
                        'error_type': 'database_error'
                    }

                # 提取需要的ID，避免会话外访问
                principal_id = principal.id
                account_id_qc = account.account_id_qc

                # 检查是否已上传
                existing = db.query(PlatformCreative).filter_by(
                    local_creative_id=local_creative_id,
                    account_id=account_id
                ).first()

                if existing:
                    return {
                        'success': True,
                        'file_path': file_path,
                        'message': '已存在，跳过上传',
                        'platform_creative_id': existing.id,
                        'skipped': True
                    }

            # 第二阶段：执行上传（无数据库连接）
            if not md5_hash:
                md5_hash = self.compute_file_md5(file_path)
                if not md5_hash:
                    return {
                        'success': False,
                        'file_path': file_path,
                        'error': 'MD5计算失败',
                        'error_type': 'file_error'
                    }

            # 创建API客户端
            client = QianchuanClient(
                app_id=self.app_settings['api_credentials']['app_id'],
                secret=self.app_settings['api_credentials']['secret'],
                principal_id=principal_id
            )
            
            # 执行上传（使用配置的超时时间）
            upload_result = client.upload_video(
                advertiser_id=account_id_qc,
                video_file_path=file_path,
                video_signature=md5_hash,
                timeout=self.upload_timeout
            )
            
            if not upload_result or 'video_id' not in upload_result:
                return {
                    'success': False,
                    'file_path': file_path,
                    'error': '上传API调用失败',
                    'error_type': 'api_error'
                }
            
            video_id = upload_result['video_id']
            material_id = upload_result.get('material_id')
            
            # 第三阶段：快速数据库写入（短连接）
            with database_session() as db:
                # 获取视频详细信息（带延迟和重试）
                time.sleep(self.library_query_delay)  # 等待API处理

                library_result = None
                for attempt in range(self.library_query_retries):
                    try:
                        library_result = client.get_library_videos(
                            advertiser_id=int(account_id_qc),
                            filtering={"video_ids": [video_id]}
                        )
                        if library_result and 'list' in library_result and library_result['list']:
                            break
                        else:
                            logger.warning(f"反查尝试 {attempt + 1}/{self.library_query_retries}: 未获取到视频信息")
                            if attempt < self.library_query_retries - 1:
                                time.sleep(2)  # 重试间隔
                    except Exception as e:
                        logger.warning(f"反查尝试 {attempt + 1}/{self.library_query_retries} 失败: {e}")
                        if attempt < self.library_query_retries - 1:
                            time.sleep(2)  # 重试间隔
                
                if library_result and 'list' in library_result and library_result['list']:
                    video_data = library_result['list'][0]
                    poster_url = video_data.get("poster_url")

                    # 使用正确的封面ID提取函数
                    video_cover_id = extract_cover_id_from_url(poster_url)

                    if not video_cover_id:
                        logger.warning(f"无法从poster_url提取封面ID: {poster_url}")
                        return {
                            'success': False,
                            'file_path': file_path,
                            'error': f'无法提取封面ID: {poster_url}',
                            'error_type': 'cover_id_error'
                        }
                    
                    # 创建平台素材记录
                    platform_creative = PlatformCreative(
                        local_creative_id=local_creative_id,
                        account_id=account_id,
                        material_id_qc=str(material_id) if material_id else None,
                        video_id=video_id,
                        video_url=video_data.get("url"),
                        video_cover_id=video_cover_id,
                        review_status='pending',
                        promotion_status='launched'
                    )
                    
                    db.add(platform_creative)
                    db.commit()
                    db.refresh(platform_creative)
                    
                    return {
                        'success': True,
                        'file_path': file_path,
                        'platform_creative_id': platform_creative.id,
                        'video_id': video_id,
                        'material_id': material_id,
                        'video_url': video_data.get("url")
                    }
                else:
                    return {
                        'success': False,
                        'file_path': file_path,
                        'error': '无法获取视频详细信息',
                        'error_type': 'api_error'
                    }
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()

            # 获取安全的file_path
            safe_file_path = upload_task.get('file_path', 'unknown_file') if isinstance(upload_task, dict) else 'unknown_file'

            logger.error(f"上传视频失败: {safe_file_path} - {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            logger.error(f"详细错误信息: {error_details}")

            # 记录任务数据用于调试
            if isinstance(upload_task, dict):
                logger.error(f"任务数据: {upload_task}")
            else:
                logger.error(f"任务数据类型错误: {type(upload_task)}")

            return {
                'success': False,
                'file_path': safe_file_path,
                'error': str(e),
                'error_type': 'unknown_error',
                'error_details': error_details
            }

    def batch_upload_videos(self, upload_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量上传视频 - 主要入口函数
        
        Args:
            upload_tasks: 上传任务列表
            
        Returns:
            上传结果列表
        """
        if not upload_tasks:
            return []
        
        logger.info(f"开始批量上传: {len(upload_tasks)} 个任务")
        start_time = time.time()
        
        # 预计算MD5（如果启用）
        if self.enable_md5_cache:
            # 安全地提取文件路径，跳过不完整的任务
            file_paths = []
            for task in upload_tasks:
                if isinstance(task, dict) and 'file_path' in task:
                    file_paths.append(task['file_path'])
                else:
                    logger.warning(f"跳过不完整的任务: {task}")

            if file_paths:
                md5_results = self.batch_compute_md5(file_paths)

                # 将MD5结果添加到任务中
                for task in upload_tasks:
                    if isinstance(task, dict) and 'file_path' in task and task['file_path'] in md5_results:
                        task['md5_hash'] = md5_results[task['file_path']]
        
        # 分批处理（带延迟）
        all_results = []
        total_batches = (len(upload_tasks) + self.batch_size - 1) // self.batch_size

        for i in range(0, len(upload_tasks), self.batch_size):
            batch_num = i // self.batch_size + 1
            batch = upload_tasks[i:i + self.batch_size]

            logger.info(f"开始处理批次 {batch_num}/{total_batches}")
            batch_results = self._process_upload_batch(batch, batch_num)
            all_results.extend(batch_results)

            # 批次间延迟（除了最后一个批次）
            if batch_num < total_batches:
                logger.info(f"批次 {batch_num} 完成，等待 {self.batch_delay} 秒...")
                time.sleep(self.batch_delay)
        
        # 统计结果
        success_count = sum(1 for r in all_results if r.get('success'))
        skipped_count = sum(1 for r in all_results if r.get('skipped'))
        failed_count = len(all_results) - success_count
        
        duration = time.time() - start_time
        throughput = len(upload_tasks) / duration * 60 if duration > 0 else 0
        
        logger.info(f"批量上传完成: {success_count} 成功, {skipped_count} 跳过, {failed_count} 失败")
        logger.info(f"耗时: {duration:.1f}秒, 吞吐量: {throughput:.1f} 视频/分钟")
        
        return all_results

    def _process_upload_batch(self, batch: List[Dict], batch_num: int) -> List[Dict]:
        """
        处理单个批次的上传
        
        Args:
            batch: 批次任务列表
            batch_num: 批次编号
            
        Returns:
            批次上传结果
        """
        logger.info(f"处理批次 {batch_num}: {len(batch)} 个任务")
        
        results = []
        with ThreadPoolExecutor(max_workers=min(len(batch), self.max_workers)) as executor:
            future_to_task = {}
            
            for task in batch:
                future = executor.submit(self.upload_single_video, task)
                future_to_task[future] = task
            
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # 安全地获取文件名用于日志
                    safe_filename = 'unknown_file'
                    if isinstance(task, dict) and 'file_path' in task:
                        safe_filename = os.path.basename(task['file_path'])

                    if result.get('success'):
                        if result.get('skipped'):
                            logger.debug(f"跳过: {safe_filename}")
                        else:
                            logger.success(f"上传成功: {safe_filename}")
                    else:
                        logger.error(f"上传失败: {safe_filename} - {result.get('error')}")

                except Exception as e:
                    # 安全地获取文件路径
                    safe_file_path = 'unknown_file'
                    safe_filename = 'unknown_file'
                    if isinstance(task, dict) and 'file_path' in task:
                        safe_file_path = task['file_path']
                        safe_filename = os.path.basename(safe_file_path)

                    logger.error(f"任务执行异常: {safe_filename} - {e}")
                    results.append({
                        'success': False,
                        'file_path': safe_file_path,
                        'error': str(e),
                        'error_type': 'execution_error'
                    })
        
        batch_success = sum(1 for r in results if r.get('success'))
        logger.info(f"批次 {batch_num} 完成: {batch_success}/{len(batch)} 成功")
        
        return results

    def get_upload_statistics(self) -> Dict[str, Any]:
        """
        获取上传统计信息
        
        Returns:
            统计信息
        """
        return {
            'cache_stats': {
                'md5_cache_size': len(self.md5_cache),
                'validation_cache_size': len(self.validation_cache),
                'md5_cache_enabled': self.enable_md5_cache,
                'validation_cache_enabled': self.enable_validation_cache
            },
            'config': {
                'max_workers': self.max_workers,
                'batch_size': self.batch_size,
                'batch_db_enabled': self.enable_batch_db
            }
        }

    def clear_cache(self):
        """清理缓存"""
        self.md5_cache.clear()
        self.validation_cache.clear()
        logger.info("上传器缓存已清理")
