---
type: "always_apply"
---

# AI生成文件管理规范

**版本**: v1.0  
**适用项目**: 千川自动化项目  
**制定时间**: 2025-07-18  
**目标**: 防止AI生成的临时文件污染项目结构，建立科学的文件管理机制

---

## 📋 管理原则

### 核心原则
1. **隔离原则**: AI生成文件与项目核心文件严格分离
2. **分类原则**: 按用途和生命周期进行分类管理
3. **标识原则**: 统一命名规范，便于识别和管理
4. **时效原则**: 明确生命周期，定期清理过期文件
5. **安全原则**: 删除前确保无依赖关系，保证项目安全

---

## 🗂️ 文件分类规则

### 1. 长期保留文件 (KEEP)
**定义**: 具有长期价值，需要永久保留的AI生成文件

**判断标准**:
- 解决了项目长期存在的问题
- 提供了可复用的工具或脚本
- 包含重要的项目分析或架构设计
- 用户明确表示需要保留

**示例**:
```
ai_tools/maintenance/cleanup_failed_materials.py
ai_tools/analysis/performance_optimization_guide.md
ai_tools/migration/database_upgrade_script.py
```

**存放位置**: `ai_tools/` 目录下按功能分类

### 2. 临时使用文件 (TEMP)
**定义**: 完成特定任务后可以删除的临时文件

**判断标准**:
- 一次性使用的分析脚本
- 临时的数据处理工具
- 特定问题的调试脚本
- 实验性的代码片段

**示例**:
```
ai_temp/cleanup/temp_20250718_cleanup_docs.py
ai_temp/analysis/temp_20250718_file_audit.py
ai_temp/debug/temp_20250718_celery_debug.py
```

**存放位置**: `ai_temp/` 目录下按日期和用途分类
**生命周期**: 默认7天，任务完成后可立即删除

### 3. 报告文档文件 (REPORT)
**定义**: AI生成的分析报告、审计报告等文档

**判断标准**:
- 项目状态分析报告
- 代码审计报告
- 性能分析报告
- 问题诊断报告

**示例**:
```
ai_reports/audit/20250718_deep_file_audit_report.md
ai_reports/analysis/20250718_performance_analysis.md
ai_reports/security/20250718_security_audit.md
```

**存放位置**: `ai_reports/` 目录下按类型和日期分类
**生命周期**: 30天，重要报告可移至doc/目录永久保留

### 4. 配置模板文件 (TEMPLATE)
**定义**: AI生成的配置文件模板和示例

**判断标准**:
- 配置文件模板
- 部署脚本模板
- 环境配置示例
- 最佳实践模板

**示例**:
```
ai_templates/config/settings_template.yml
ai_templates/deploy/docker_compose_template.yml
ai_templates/scripts/backup_template.sh
```

**存放位置**: `ai_templates/` 目录下按类型分类
**生命周期**: 长期保留，定期更新

---

## 🏷️ 命名规范

### 统一命名格式
```
[前缀]_[日期]_[用途]_[描述].[扩展名]
```

### 前缀规则
- `ai_tool_`: 长期保留的工具脚本
- `ai_temp_`: 临时使用的脚本
- `ai_report_`: 分析报告文档
- `ai_template_`: 配置模板文件
- `ai_debug_`: 调试脚本
- `ai_analysis_`: 分析脚本

### 日期格式
- 使用 `YYYYMMDD` 格式
- 如需精确到时间，使用 `YYYYMMDD_HHMM` 格式

### 用途标识
- `cleanup`: 清理相关
- `analysis`: 分析相关
- `debug`: 调试相关
- `migration`: 迁移相关
- `optimization`: 优化相关
- `security`: 安全相关
- `maintenance`: 维护相关

### 命名示例
```python
# 正确命名
ai_temp_20250718_cleanup_docs_directory.py
ai_report_20250718_analysis_file_audit.md
ai_tool_20250718_maintenance_zombie_cleaner.py
ai_template_20250718_config_celery_settings.yml

# 错误命名
deep_cleanup_docs.py                    # 缺少前缀和日期
file_audit_report.md                    # 缺少AI标识
cleanup_script.py                       # 命名过于简单
```

---

## 📁 存放位置规则

### 目录结构
```
qianchuangzl/
├── ai_tools/                    # AI生成的长期工具
│   ├── maintenance/             # 维护工具
│   ├── analysis/                # 分析工具
│   ├── cleanup/                 # 清理工具
│   ├── migration/               # 迁移工具
│   └── optimization/            # 优化工具
├── ai_temp/                     # AI生成的临时文件
│   ├── cleanup/                 # 临时清理脚本
│   ├── debug/                   # 临时调试脚本
│   ├── analysis/                # 临时分析脚本
│   └── experiment/              # 实验性脚本
├── ai_reports/                  # AI生成的报告文档
│   ├── audit/                   # 审计报告
│   ├── analysis/                # 分析报告
│   ├── security/                # 安全报告
│   └── performance/             # 性能报告
├── ai_templates/                # AI生成的模板文件
│   ├── config/                  # 配置模板
│   ├── scripts/                 # 脚本模板
│   └── deploy/                  # 部署模板
└── .gitignore                   # 忽略AI临时文件
```

### 目录权限和访问规则
- `ai_tools/`: 需要代码审查后才能提交到git
- `ai_temp/`: 不提交到git，本地临时使用
- `ai_reports/`: 重要报告可选择性提交到git
- `ai_templates/`: 经过验证的模板提交到git

---

## ⏰ 生命周期管理

### 文件生命周期定义

#### 临时文件 (ai_temp/)
- **创建**: AI对话中生成
- **使用期**: 1-7天
- **清理条件**: 
  - 任务完成后立即删除
  - 超过7天自动清理
  - 用户手动确认删除

#### 报告文件 (ai_reports/)
- **创建**: AI分析生成
- **保留期**: 30天
- **清理条件**:
  - 重要报告移至doc/目录永久保留
  - 一般报告30天后自动清理
  - 过时报告手动删除

#### 工具文件 (ai_tools/)
- **创建**: AI生成有价值工具
- **保留期**: 永久保留
- **清理条件**:
  - 功能被替代时删除
  - 不再适用当前项目时删除
  - 经过团队评审决定删除

#### 模板文件 (ai_templates/)
- **创建**: AI生成配置模板
- **保留期**: 长期保留
- **更新策略**: 定期更新，保持最新

### 自动清理机制

#### 每日清理任务
```python
# 添加到 config/settings.yml
ai_file_management:
  auto_cleanup:
    enabled: true
    daily_cleanup_time: "02:00"  # 凌晨2点执行
    temp_file_retention_days: 7
    report_file_retention_days: 30
  cleanup_rules:
    - pattern: "ai_temp/**/*"
      max_age_days: 7
    - pattern: "ai_reports/**/*"
      max_age_days: 30
      exclude_patterns:
        - "*_important_*"
        - "*_keep_*"
```

#### 手动清理命令
```bash
# 清理所有过期临时文件
python tools/ai_file_manager.py cleanup --type temp

# 清理过期报告文件
python tools/ai_file_manager.py cleanup --type reports

# 显示AI文件统计
python tools/ai_file_manager.py status

# 交互式清理
python tools/ai_file_manager.py cleanup --interactive
```

---

## 🔄 使用后处理流程

### 任务完成后的处理步骤

#### 1. 文件价值评估
```
用户评估 → AI生成文件是否有长期价值？
├── 是 → 移动到ai_tools/目录，重命名为永久文件
├── 否 → 继续下一步评估
└── 不确定 → 暂时保留，标记为待评估
```

#### 2. 文件分类处理
```python
# 示例：任务完成后的文件处理
def handle_ai_generated_files(task_type, files_list):
    for file_path in files_list:
        if is_valuable_tool(file_path):
            move_to_permanent_location(file_path)
        elif is_important_report(file_path):
            move_to_reports_archive(file_path)
        else:
            schedule_for_cleanup(file_path)
```

#### 3. 用户确认机制
- **自动删除**: 明确的临时文件（如调试脚本）
- **用户确认**: 可能有价值的文件
- **团队评审**: 重要的工具或分析报告

### 处理决策树
```
AI生成文件 → 任务完成
├── 解决了长期问题？
│   ├── 是 → 移至ai_tools/ → 永久保留
│   └── 否 → 继续评估
├── 包含重要分析？
│   ├── 是 → 移至ai_reports/ → 30天保留
│   └── 否 → 继续评估
├── 仅为临时调试？
│   ├── 是 → 立即删除
│   └── 否 → 移至ai_temp/ → 7天保留
└── 不确定价值？
    └── 用户确认 → 按确认结果处理
```

---

## 🛠️ 实施工具

### AI文件管理器脚本
需要创建 `tools/ai_file_manager.py` 工具，提供以下功能：

1. **文件分类**: 自动识别和分类AI生成文件
2. **生命周期管理**: 根据规则自动清理过期文件
3. **统计报告**: 显示AI文件使用情况
4. **交互式管理**: 提供用户友好的管理界面
5. **安全检查**: 删除前检查文件依赖关系

### 集成到项目工作流
```yaml
# 添加到 config/settings.yml
ai_file_management:
  enabled: true
  auto_create_directories: true
  naming_validation: true
  lifecycle_management: true
  cleanup_notifications: true
```

---

## 📊 监控和统计

### 定期统计报告
- AI文件生成数量和类型
- 存储空间使用情况
- 清理效果统计
- 文件价值转化率

### 告警机制
- AI临时文件超过阈值时告警
- 长期未清理的文件提醒
- 存储空间不足预警

---

## ✅ 实施检查清单

### 初始设置
- [ ] 创建AI文件管理目录结构
- [ ] 配置.gitignore忽略临时文件
- [ ] 实现ai_file_manager.py工具
- [ ] 更新项目文档说明

### 日常使用
- [ ] AI生成文件时遵循命名规范
- [ ] 任务完成后及时处理文件
- [ ] 定期检查和清理过期文件
- [ ] 重要文件及时归档

### 定期维护
- [ ] 每周检查AI文件使用情况
- [ ] 每月评估文件管理规则效果
- [ ] 季度更新管理规范
- [ ] 年度归档重要AI生成内容

---

通过这套管理规范，可以有效防止AI生成文件污染项目结构，同时确保有价值的AI生成内容得到妥善保存和管理。
