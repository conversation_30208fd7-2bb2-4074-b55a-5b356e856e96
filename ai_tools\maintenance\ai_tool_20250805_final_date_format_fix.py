#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 维护工具
生命周期: 永久保留
创建目的: 彻底修复日期格式不一致问题，确保系统只使用YYYYMMDD格式
清理条件: 问题彻底解决后可归档保留
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger

class FinalDateFormatFixer:
    """最终日期格式修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.base_workflow_dir = "D:/workflow_assets"
        self.principal_name = "缇萃百货"
        
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'directories_found': [],
            'directories_unified': [],
            'files_moved': 0,
            'errors': []
        }

    def scan_and_fix_directories(self):
        """扫描并修复所有日期格式不一致的目录"""
        logger.info("🔍 扫描日期格式不一致的目录...")
        
        # 检查所有可能的工作流目录
        workflow_dirs = [
            "00_uploaded_archive",
            "03_materials_approved",
            "03_materials_under_review"  # 旧目录名
        ]
        
        total_fixed = 0
        
        for workflow_dir in workflow_dirs:
            dir_path = os.path.join(self.base_workflow_dir, workflow_dir, self.principal_name)
            
            if not os.path.exists(dir_path):
                logger.info(f"⏭️ 目录不存在，跳过: {dir_path}")
                continue
            
            logger.info(f"🔍 检查目录: {dir_path}")
            fixed_count = self._fix_date_directories_in_path(dir_path)
            total_fixed += fixed_count
        
        logger.success(f"✅ 总共修复了 {total_fixed} 个目录的日期格式问题")
        return total_fixed

    def _fix_date_directories_in_path(self, base_path: str) -> int:
        """修复指定路径下的日期目录格式"""
        if not os.path.exists(base_path):
            return 0
        
        fixed_count = 0
        
        # 获取所有子目录
        subdirs = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]
        
        # 分类目录
        dash_dirs = []
        standard_dirs = []
        
        for subdir in subdirs:
            if self._is_dash_date_format(subdir):
                dash_dirs.append(subdir)
            elif self._is_standard_date_format(subdir):
                standard_dirs.append(subdir)
        
        logger.info(f"  📊 发现带连字符目录: {len(dash_dirs)} 个")
        logger.info(f"  📊 发现标准格式目录: {len(standard_dirs)} 个")
        
        # 修复带连字符的目录
        for dash_dir in dash_dirs:
            try:
                success = self._migrate_dash_directory(base_path, dash_dir)
                if success:
                    fixed_count += 1
            except Exception as e:
                logger.error(f"❌ 修复目录失败: {dash_dir} - {e}")
                self.results['errors'].append(f"修复失败: {dash_dir} - {str(e)}")
        
        return fixed_count

    def _is_dash_date_format(self, dirname: str) -> bool:
        """检查是否为带连字符的日期格式 (YYYY-MM-DD)"""
        if len(dirname) != 10 or dirname.count('-') != 2:
            return False
        
        try:
            datetime.strptime(dirname, '%Y-%m-%d')
            return True
        except ValueError:
            return False

    def _is_standard_date_format(self, dirname: str) -> bool:
        """检查是否为标准日期格式 (YYYYMMDD)"""
        if len(dirname) != 8:
            return False
        
        try:
            datetime.strptime(dirname, '%Y%m%d')
            return True
        except ValueError:
            return False

    def _migrate_dash_directory(self, base_path: str, dash_dir: str) -> bool:
        """迁移带连字符的目录到标准格式"""
        try:
            # 转换格式
            date_obj = datetime.strptime(dash_dir, '%Y-%m-%d')
            standard_dir = date_obj.strftime('%Y%m%d')
            
            source_path = os.path.join(base_path, dash_dir)
            target_path = os.path.join(base_path, standard_dir)
            
            logger.info(f"  🔄 {dash_dir} -> {standard_dir}")
            
            # 检查源目录中的文件
            if not os.path.exists(source_path):
                logger.warning(f"  ⚠️ 源目录不存在: {source_path}")
                return False
            
            files = os.listdir(source_path)
            if not files:
                logger.info(f"  📁 源目录为空，直接删除: {dash_dir}")
                os.rmdir(source_path)
                return True
            
            # 创建目标目录
            os.makedirs(target_path, exist_ok=True)
            
            # 移动文件
            files_moved = 0
            for filename in files:
                src_file = os.path.join(source_path, filename)
                dst_file = os.path.join(target_path, filename)
                
                if os.path.isfile(src_file):
                    if not os.path.exists(dst_file):
                        shutil.move(src_file, dst_file)
                        files_moved += 1
                        logger.debug(f"    ✅ 移动文件: {filename}")
                    else:
                        logger.warning(f"    ⚠️ 目标文件已存在，跳过: {filename}")
                elif os.path.isdir(src_file):
                    # 处理子目录
                    if not os.path.exists(dst_file):
                        shutil.move(src_file, dst_file)
                        files_moved += 1
                        logger.debug(f"    ✅ 移动目录: {filename}")
            
            # 删除空的源目录
            remaining_items = os.listdir(source_path)
            if not remaining_items:
                os.rmdir(source_path)
                logger.success(f"  🗑️ 删除空目录: {dash_dir}")
            else:
                logger.warning(f"  ⚠️ 源目录不为空，保留: {dash_dir} (剩余 {len(remaining_items)} 项)")
            
            self.results['files_moved'] += files_moved
            self.results['directories_unified'].append({
                'source': dash_dir,
                'target': standard_dir,
                'files_moved': files_moved,
                'base_path': base_path
            })
            
            logger.success(f"  ✅ 成功迁移: {dash_dir} -> {standard_dir} ({files_moved} 个文件)")
            return True
            
        except Exception as e:
            logger.error(f"  ❌ 迁移失败: {dash_dir} - {e}")
            return False

    def verify_no_dash_directories(self) -> bool:
        """验证系统中不再存在带连字符的日期目录"""
        logger.info("🔍 验证系统中不再存在带连字符的日期目录...")
        
        workflow_dirs = [
            "00_uploaded_archive",
            "03_materials_approved",
            "03_materials_under_review"
        ]
        
        found_dash_dirs = []
        
        for workflow_dir in workflow_dirs:
            dir_path = os.path.join(self.base_workflow_dir, workflow_dir, self.principal_name)
            
            if not os.path.exists(dir_path):
                continue
            
            subdirs = [d for d in os.listdir(dir_path) if os.path.isdir(os.path.join(dir_path, d))]
            
            for subdir in subdirs:
                if self._is_dash_date_format(subdir):
                    found_dash_dirs.append(os.path.join(dir_path, subdir))
        
        if found_dash_dirs:
            logger.error(f"❌ 仍然发现 {len(found_dash_dirs)} 个带连字符的目录:")
            for dash_dir in found_dash_dirs:
                logger.error(f"  - {dash_dir}")
            return False
        else:
            logger.success("✅ 验证通过：系统中不再存在带连字符的日期目录")
            return True

    def generate_final_report(self):
        """生成最终修复报告"""
        report_path = self.project_root / 'ai_reports' / 'maintenance' / f'ai_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}_final_date_format_fix.json'
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 最终修复报告已保存: {report_path}")

    def run_complete_fix(self):
        """运行完整的日期格式修复"""
        logger.info("🚀 开始最终日期格式修复...")
        
        try:
            # 1. 扫描并修复目录
            fixed_count = self.scan_and_fix_directories()
            
            # 2. 验证修复结果
            verification_passed = self.verify_no_dash_directories()
            
            # 3. 生成报告
            self.results['fix_summary'] = {
                'directories_fixed': fixed_count,
                'verification_passed': verification_passed,
                'total_files_moved': self.results['files_moved'],
                'errors_count': len(self.results['errors'])
            }
            
            self.generate_final_report()
            
            if verification_passed and fixed_count >= 0:
                logger.success("🎉 日期格式修复完成！系统现在使用统一的YYYYMMDD格式")
                return True
            else:
                logger.error("❌ 日期格式修复未完全成功，请检查错误信息")
                return False
                
        except Exception as e:
            logger.error(f"❌ 修复过程发生错误: {e}")
            self.results['errors'].append(f"修复过程错误: {str(e)}")
            return False

def main():
    """主函数"""
    fixer = FinalDateFormatFixer()
    
    print("🎯 千川自动化项目 - 最终日期格式修复工具")
    print("="*60)
    print("📌 目标：彻底解决日期格式不一致问题")
    print("📌 标准：统一使用 YYYYMMDD 格式")
    print("="*60)
    
    success = fixer.run_complete_fix()
    
    if success:
        print("\n🎉 修复成功！")
        print("✅ 系统现在使用统一的日期格式")
        print("✅ 不会再出现重复目录问题")
    else:
        print("\n❌ 修复失败！")
        print("请检查错误日志并手动处理剩余问题")
    
    return success

if __name__ == "__main__":
    main()
