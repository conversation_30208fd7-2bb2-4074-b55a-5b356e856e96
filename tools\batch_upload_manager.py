#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量上传管理器 - 核心工具
提供高性能批量视频上传的命令行接口和管理功能
"""

import argparse
import os
import sys
import time
from pathlib import Path
from typing import List, Dict, Any

# --- 路径设置 ---
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.config_manager import get_config_manager
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import AdAccount, Principal
from qianchuan_aw.workflows.batch_uploader import BatchUploader
from qianchuan_aw.utils.file_utils import get_file_md5
from qianchuan_aw.utils.workflow_helpers import find_or_create_local_creative


class BatchUploadManager:
    """批量上传管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.config_manager = get_config_manager()
        self.app_settings = self.config_manager.get_config()
        self.batch_uploader = BatchUploader(self.app_settings)
        
    def scan_video_files(self, directory: str) -> List[str]:
        """
        扫描目录中的视频文件
        
        Args:
            directory: 目录路径
            
        Returns:
            视频文件路径列表
        """
        video_extensions = ['.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv']
        video_files = []
        
        directory_path = Path(directory)
        if not directory_path.exists():
            logger.error(f"目录不存在: {directory}")
            return []
        
        for ext in video_extensions:
            video_files.extend(directory_path.glob(f"*{ext}"))
            video_files.extend(directory_path.glob(f"*{ext.upper()}"))
        
        video_files = [str(f) for f in video_files]
        logger.info(f"扫描到 {len(video_files)} 个视频文件")
        
        return video_files
    
    def prepare_upload_tasks(self, video_files: List[str], principal_name: str, account_name: str) -> List[Dict[str, Any]]:
        """
        准备上传任务
        
        Args:
            video_files: 视频文件列表
            principal_name: 主体名称
            account_name: 账户名称
            
        Returns:
            上传任务列表
        """
        upload_tasks = []
        
        with database_session() as db:
            # 获取主体和账户
            principal = db.query(Principal).filter_by(name=principal_name).first()
            if not principal:
                logger.error(f"找不到主体: {principal_name}")
                return []
            
            account = db.query(AdAccount).filter_by(name=account_name).first()
            if not account:
                logger.error(f"找不到账户: {account_name}")
                return []
            
            logger.info(f"准备上传任务: 主体={principal_name}, 账户={account_name}")
            
            for video_file in video_files:
                try:
                    # 计算文件MD5
                    file_hash = get_file_md5(video_file)
                    if not file_hash:
                        logger.warning(f"无法计算MD5，跳过文件: {video_file}")
                        continue

                    # 获取或创建本地素材记录
                    local_creative = find_or_create_local_creative(db, principal.id, file_hash, video_file)
                    if not local_creative or not local_creative.id:
                        logger.error(f"无法创建本地素材记录，跳过文件: {video_file}")
                        continue

                    # 验证所有必需数据都存在
                    if not all([video_file, local_creative.id, account.id, principal_name]):
                        logger.error(f"任务数据不完整，跳过文件: {video_file}")
                        continue

                    upload_tasks.append({
                        'file_path': video_file,
                        'local_creative_id': local_creative.id,
                        'account_id': account.id,
                        'principal_name': principal_name
                    })

                except Exception as e:
                    logger.error(f"准备任务失败: {video_file} - {e}")
                    continue
        
        logger.info(f"准备完成: {len(upload_tasks)} 个上传任务")
        return upload_tasks
    
    def execute_batch_upload(self, upload_tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        执行批量上传
        
        Args:
            upload_tasks: 上传任务列表
            
        Returns:
            上传结果统计
        """
        if not upload_tasks:
            return {'success': 0, 'failed': 0, 'skipped': 0, 'total': 0}
        
        logger.info(f"开始执行批量上传: {len(upload_tasks)} 个任务")
        start_time = time.time()
        
        # 执行上传
        results = self.batch_uploader.batch_upload_videos(upload_tasks)
        
        # 统计结果
        success_count = sum(1 for r in results if r.get('success') and not r.get('skipped'))
        skipped_count = sum(1 for r in results if r.get('skipped'))
        failed_count = len(results) - success_count - skipped_count
        
        duration = time.time() - start_time
        throughput = len(upload_tasks) / duration * 60 if duration > 0 else 0
        
        stats = {
            'success': success_count,
            'failed': failed_count,
            'skipped': skipped_count,
            'total': len(upload_tasks),
            'duration_seconds': duration,
            'throughput_per_minute': throughput
        }
        
        logger.info(f"批量上传完成: {success_count} 成功, {skipped_count} 跳过, {failed_count} 失败")
        logger.info(f"耗时: {duration:.1f}秒, 吞吐量: {throughput:.1f} 视频/分钟")
        
        return stats
    
    def upload_from_directory(self, directory: str, principal_name: str, account_name: str) -> Dict[str, Any]:
        """
        从目录批量上传视频
        
        Args:
            directory: 视频目录
            principal_name: 主体名称
            account_name: 账户名称
            
        Returns:
            上传结果统计
        """
        # 1. 扫描视频文件
        video_files = self.scan_video_files(directory)
        if not video_files:
            logger.error("没有找到视频文件")
            return {'success': 0, 'failed': 0, 'skipped': 0, 'total': 0}
        
        # 2. 准备上传任务
        upload_tasks = self.prepare_upload_tasks(video_files, principal_name, account_name)
        if not upload_tasks:
            logger.error("没有有效的上传任务")
            return {'success': 0, 'failed': 0, 'skipped': 0, 'total': 0}
        
        # 3. 执行批量上传
        return self.execute_batch_upload(upload_tasks)
    
    def get_uploader_stats(self) -> Dict[str, Any]:
        """获取上传器统计信息"""
        return self.batch_uploader.get_upload_statistics()
    
    def clear_cache(self):
        """清理缓存"""
        self.batch_uploader.clear_cache()
        logger.info("批量上传器缓存已清理")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量上传管理器 - 高性能视频上传工具')
    parser.add_argument('--directory', '-d', required=True, help='视频文件目录')
    parser.add_argument('--principal', '-p', required=True, help='主体名称')
    parser.add_argument('--account', '-a', required=True, help='账户名称')
    parser.add_argument('--stats', action='store_true', help='显示上传器统计信息')
    parser.add_argument('--clear-cache', action='store_true', help='清理缓存')
    
    args = parser.parse_args()
    
    try:
        manager = BatchUploadManager()
        
        if args.clear_cache:
            manager.clear_cache()
            return
        
        if args.stats:
            stats = manager.get_uploader_stats()
            print("📊 上传器统计信息:")
            print(f"  缓存状态: MD5缓存={stats['cache_stats']['md5_cache_size']}项")
            print(f"  配置信息: 并发数={stats['config']['max_workers']}, 批次大小={stats['config']['batch_size']}")
            return
        
        # 执行批量上传
        logger.info("🚀 千川自动化 - 批量上传管理器")
        logger.info(f"📁 目录: {args.directory}")
        logger.info(f"👤 主体: {args.principal}")
        logger.info(f"🏢 账户: {args.account}")
        logger.info("="*60)
        
        result = manager.upload_from_directory(args.directory, args.principal, args.account)
        
        print("\n" + "="*60)
        print("📊 上传结果统计:")
        print(f"  ✅ 成功: {result['success']} 个")
        print(f"  ⏭️ 跳过: {result['skipped']} 个")
        print(f"  ❌ 失败: {result['failed']} 个")
        print(f"  📈 总计: {result['total']} 个")
        print(f"  ⏱️ 耗时: {result['duration_seconds']:.1f} 秒")
        print(f"  🚀 吞吐量: {result['throughput_per_minute']:.1f} 视频/分钟")
        
        if result['success'] > 0:
            print(f"\n🎉 批量上传成功完成！")
        elif result['failed'] > 0:
            print(f"\n⚠️ 部分上传失败，请检查日志")
        else:
            print(f"\n💡 所有文件都已存在，无需重复上传")
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"批量上传失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
