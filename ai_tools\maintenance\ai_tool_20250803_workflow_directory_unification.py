#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 统一工作流目录路径，解决路径混乱问题
清理条件: 功能被替代时删除
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

from loguru import logger
from qianchuan_aw.utils.config_loader import load_settings

class WorkflowDirectoryUnifier:
    """工作流目录统一管理器"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.base_dir = self.app_settings.get('custom_workflow_assets_dir', 'D:/workflow_assets')
        self.principal_name = "缇萃百货"
        
        # 定义标准目录结构（只保留必要的3个目录）
        self.standard_directories = {
            "01_materials_to_process": "待处理素材目录",
            "00_uploaded_archive": "上传存档目录", 
            "03_materials_approved": "审核通过素材目录"
        }
        
        # 需要清理的混乱目录
        self.deprecated_directories = [
            "01_to_process",  # 混乱的目录名
            "00_materials_archived",  # 旧的命名
            "03_harvested_materials",  # 旧的命名
            "02_materials_in_testing",  # 不需要的目录
            "04_materials_rejected",  # 不需要的目录
            "05_manual_promotion",  # 不需要的目录
            "06_materials_rejected",  # 不需要的目录
            "07_materials_cleaned"  # 不需要的目录
        ]
        
        self.migration_log = []
    
    def analyze_current_structure(self) -> Dict[str, Any]:
        """分析当前目录结构"""
        logger.info("🔍 分析当前工作流目录结构...")
        
        analysis = {
            "base_dir": self.base_dir,
            "existing_dirs": {},
            "file_counts": {},
            "issues": []
        }
        
        if not os.path.exists(self.base_dir):
            analysis["issues"].append(f"基础目录不存在: {self.base_dir}")
            return analysis
        
        # 扫描所有子目录
        for item in os.listdir(self.base_dir):
            item_path = os.path.join(self.base_dir, item)
            if os.path.isdir(item_path):
                analysis["existing_dirs"][item] = item_path
                
                # 统计文件数量
                principal_dir = os.path.join(item_path, self.principal_name)
                if os.path.exists(principal_dir):
                    file_count = sum(len(files) for _, _, files in os.walk(principal_dir))
                    analysis["file_counts"][item] = file_count
                else:
                    analysis["file_counts"][item] = 0
        
        # 检查问题
        for deprecated in self.deprecated_directories:
            if deprecated in analysis["existing_dirs"]:
                file_count = analysis["file_counts"].get(deprecated, 0)
                analysis["issues"].append(f"发现混乱目录: {deprecated} (包含{file_count}个文件)")
        
        return analysis
    
    def create_standard_structure(self):
        """创建标准目录结构"""
        logger.info("📁 创建标准目录结构...")
        
        for dir_name, description in self.standard_directories.items():
            # 创建主目录
            main_dir = os.path.join(self.base_dir, dir_name)
            os.makedirs(main_dir, exist_ok=True)
            
            # 创建主体子目录
            principal_dir = os.path.join(main_dir, self.principal_name)
            os.makedirs(principal_dir, exist_ok=True)
            
            # 在存档目录中创建日期子目录结构
            if dir_name == "00_uploaded_archive":
                today = datetime.now().strftime('%Y%m%d')
                date_dir = os.path.join(principal_dir, today)
                os.makedirs(date_dir, exist_ok=True)
            
            logger.info(f"✅ 创建目录: {dir_name} - {description}")
    
    def migrate_files(self, dry_run: bool = True) -> bool:
        """迁移文件到标准目录"""
        logger.info(f"🔄 {'预览' if dry_run else '执行'}文件迁移...")
        
        migration_plan = []
        
        # 分析需要迁移的文件
        for deprecated_dir in self.deprecated_directories:
            deprecated_path = os.path.join(self.base_dir, deprecated_dir, self.principal_name)
            if not os.path.exists(deprecated_path):
                continue
            
            # 确定目标目录
            target_dir = self._get_target_directory(deprecated_dir)
            if not target_dir:
                continue
            
            target_path = os.path.join(self.base_dir, target_dir, self.principal_name)
            
            # 扫描文件
            for root, dirs, files in os.walk(deprecated_path):
                for file in files:
                    source_file = os.path.join(root, file)
                    
                    # 计算相对路径
                    rel_path = os.path.relpath(source_file, deprecated_path)
                    
                    # 确定目标文件路径
                    if target_dir == "00_uploaded_archive":
                        # 存档目录需要按日期组织
                        today = datetime.now().strftime('%Y%m%d')
                        target_file = os.path.join(target_path, today, rel_path)
                    else:
                        target_file = os.path.join(target_path, rel_path)
                    
                    migration_plan.append({
                        'source': source_file,
                        'target': target_file,
                        'deprecated_dir': deprecated_dir,
                        'target_dir': target_dir
                    })
        
        # 显示迁移计划
        if migration_plan:
            logger.info(f"📋 发现 {len(migration_plan)} 个文件需要迁移:")
            for i, plan in enumerate(migration_plan[:10]):  # 只显示前10个
                logger.info(f"   {i+1}. {os.path.basename(plan['source'])} -> {plan['target_dir']}")
            if len(migration_plan) > 10:
                logger.info(f"   ... 还有 {len(migration_plan) - 10} 个文件")
        
        if dry_run:
            logger.info("🔍 这是预览模式，没有实际移动文件")
            return True
        
        # 执行迁移
        success_count = 0
        for plan in migration_plan:
            try:
                # 确保目标目录存在
                os.makedirs(os.path.dirname(plan['target']), exist_ok=True)
                
                # 移动文件
                shutil.move(plan['source'], plan['target'])
                success_count += 1
                
                self.migration_log.append(f"✅ {plan['source']} -> {plan['target']}")
                
            except Exception as e:
                error_msg = f"❌ 迁移失败: {plan['source']} -> {e}"
                logger.error(error_msg)
                self.migration_log.append(error_msg)
        
        logger.info(f"📊 迁移完成: {success_count}/{len(migration_plan)} 个文件成功迁移")
        return success_count == len(migration_plan)
    
    def _get_target_directory(self, deprecated_dir: str) -> str:
        """确定废弃目录的目标目录"""
        mapping = {
            "01_to_process": "01_materials_to_process",
            "00_materials_archived": "00_uploaded_archive",
            "03_harvested_materials": "03_materials_approved",
            # 其他废弃目录根据内容判断
            "02_materials_in_testing": "01_materials_to_process",  # 测试中的回到待处理
            "04_materials_rejected": None,  # 拒绝的文件不迁移
            "05_manual_promotion": "03_materials_approved",  # 手动晋升的到审核通过
            "06_materials_rejected": None,  # 拒绝的文件不迁移
            "07_materials_cleaned": None  # 清理的文件不迁移
        }
        return mapping.get(deprecated_dir)
    
    def cleanup_empty_directories(self, dry_run: bool = True):
        """清理空的废弃目录"""
        logger.info(f"🧹 {'预览' if dry_run else '执行'}空目录清理...")
        
        cleanup_count = 0
        for deprecated_dir in self.deprecated_directories:
            deprecated_path = os.path.join(self.base_dir, deprecated_dir)
            if not os.path.exists(deprecated_path):
                continue
            
            # 检查是否为空目录
            if self._is_directory_empty(deprecated_path):
                if not dry_run:
                    try:
                        shutil.rmtree(deprecated_path)
                        cleanup_count += 1
                        logger.info(f"🗑️ 删除空目录: {deprecated_dir}")
                    except Exception as e:
                        logger.error(f"❌ 删除目录失败: {deprecated_dir} -> {e}")
                else:
                    logger.info(f"🔍 将删除空目录: {deprecated_dir}")
                    cleanup_count += 1
        
        if cleanup_count > 0:
            logger.info(f"📊 {'将清理' if dry_run else '已清理'} {cleanup_count} 个空目录")
        else:
            logger.info("✅ 没有发现需要清理的空目录")
    
    def _is_directory_empty(self, path: str) -> bool:
        """检查目录是否为空（递归检查）"""
        if not os.path.exists(path):
            return True
        
        for root, dirs, files in os.walk(path):
            if files:  # 如果有文件，不为空
                return False
        return True
    
    def update_configuration(self, dry_run: bool = True) -> bool:
        """更新配置文件中的目录设置"""
        logger.info(f"⚙️ {'预览' if dry_run else '更新'}配置文件...")
        
        config_file = os.path.join(project_root, 'config', 'settings.yml')
        if not os.path.exists(config_file):
            logger.error(f"❌ 配置文件不存在: {config_file}")
            return False
        
        # 读取当前配置
        import yaml
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 更新workflow_dirs配置
        if 'workflow' not in config:
            config['workflow'] = {}
        
        # 设置标准目录配置
        config['workflow']['workflow_dirs'] = {
            'DIR_00_ARCHIVED': '00_uploaded_archive',
            'DIR_01_TO_PROCESS': '01_materials_to_process', 
            'DIR_03_MATERIALS_APPROVED': '03_materials_approved'
        }
        
        # 移除废弃的配置项
        deprecated_keys = [
            'DIR_02_UPLOADING', 'DIR_04_IN_PRODUCTION', 'DIR_05_MANUAL_PROMOTION',
            'DIR_06_REJECTED', 'DIR_07_CLEANED'
        ]
        for key in deprecated_keys:
            if key in config['workflow']['workflow_dirs']:
                del config['workflow']['workflow_dirs'][key]
        
        if dry_run:
            logger.info("🔍 配置更新预览:")
            logger.info(f"   workflow_dirs: {config['workflow']['workflow_dirs']}")
            return True
        
        # 备份原配置
        backup_file = f"{config_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(config_file, backup_file)
        logger.info(f"💾 配置文件已备份: {backup_file}")
        
        # 写入新配置
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        logger.success("✅ 配置文件已更新")
        return True

    def generate_unification_report(self) -> str:
        """生成统一化报告"""
        logger.info("📋 生成目录统一化报告...")

        analysis = self.analyze_current_structure()

        report = f"""
# 工作流目录统一化报告

**统一时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**基础目录**: {self.base_dir}
**主体名称**: {self.principal_name}

## 🎯 统一目标

### 标准目录结构（仅保留3个必要目录）
1. **01_materials_to_process** - 待处理素材目录
2. **00_uploaded_archive** - 上传存档目录（按日期组织）
3. **03_materials_approved** - 审核通过素材目录

### 日期格式统一
- 存档目录使用 YYYYMMDD 格式的日期子目录
- 例如: `00_uploaded_archive/缇萃百货/20250803/`

## 📊 当前状态分析

### 现有目录
"""

        for dir_name, file_count in analysis["file_counts"].items():
            status = "✅ 标准" if dir_name in self.standard_directories else "❌ 废弃"
            report += f"- {dir_name}: {file_count}个文件 {status}\n"

        report += f"""
### 发现的问题
"""
        for issue in analysis["issues"]:
            report += f"- {issue}\n"

        if self.migration_log:
            report += f"""
### 迁移日志
"""
            for log_entry in self.migration_log[-20:]:  # 只显示最后20条
                report += f"- {log_entry}\n"

        report += f"""
## 🔧 配置更新

### 更新的配置项
```yaml
workflow:
  workflow_dirs:
    DIR_00_ARCHIVED: 00_uploaded_archive
    DIR_01_TO_PROCESS: 01_materials_to_process
    DIR_03_MATERIALS_APPROVED: 03_materials_approved
```

### 移除的配置项
- DIR_02_UPLOADING (02_materials_in_testing)
- DIR_04_IN_PRODUCTION (04_materials_in_production)
- DIR_05_MANUAL_PROMOTION (05_manual_promotion)
- DIR_06_REJECTED (06_materials_rejected)
- DIR_07_CLEANED (07_materials_cleaned)

## 💡 使用建议

1. **重启所有服务**以应用新的目录配置
2. **验证文件迁移**确保所有重要文件都已正确迁移
3. **更新备份脚本**使用新的目录结构
4. **清理旧的硬编码路径**检查代码中的硬编码路径引用

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        # 保存报告
        report_path = f"ai_reports/maintenance/ai_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_workflow_directory_unification.md"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.success(f"📄 统一化报告已保存: {report_path}")
        return report_path

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("🔧 工作流目录统一化工具")
    logger.info("=" * 80)

    unifier = WorkflowDirectoryUnifier()

    try:
        # 1. 分析当前结构
        logger.info("🔍 第1步: 分析当前目录结构")
        analysis = unifier.analyze_current_structure()

        if analysis["issues"]:
            logger.warning(f"⚠️ 发现 {len(analysis['issues'])} 个问题需要解决")
            for issue in analysis["issues"]:
                logger.warning(f"   - {issue}")
        else:
            logger.success("✅ 当前目录结构符合标准")
            return 0

        # 2. 创建标准结构
        logger.info("📁 第2步: 创建标准目录结构")
        unifier.create_standard_structure()

        # 3. 预览文件迁移
        logger.info("🔍 第3步: 预览文件迁移计划")
        unifier.migrate_files(dry_run=True)

        # 4. 询问用户确认
        print("\n" + "="*60)
        print("⚠️  即将执行以下操作:")
        print("   1. 迁移文件到标准目录")
        print("   2. 清理空的废弃目录")
        print("   3. 更新配置文件")
        print("="*60)

        confirm = input("是否继续执行? (y/N): ").strip().lower()
        if confirm != 'y':
            logger.info("❌ 用户取消操作")
            return 0

        # 5. 执行文件迁移
        logger.info("🔄 第4步: 执行文件迁移")
        if not unifier.migrate_files(dry_run=False):
            logger.error("❌ 文件迁移失败，停止后续操作")
            return 1

        # 6. 清理空目录
        logger.info("🧹 第5步: 清理空目录")
        unifier.cleanup_empty_directories(dry_run=False)

        # 7. 更新配置
        logger.info("⚙️ 第6步: 更新配置文件")
        if not unifier.update_configuration(dry_run=False):
            logger.error("❌ 配置更新失败")
            return 1

        # 8. 生成报告
        logger.info("📋 第7步: 生成统一化报告")
        report_path = unifier.generate_unification_report()

        logger.info("=" * 80)
        logger.info("🎉 目录统一化完成!")
        logger.info("=" * 80)
        logger.success("✅ 标准目录结构已创建")
        logger.success("✅ 文件迁移已完成")
        logger.success("✅ 废弃目录已清理")
        logger.success("✅ 配置文件已更新")
        logger.info(f"📄 详细报告: {report_path}")

        logger.info("💡 后续步骤:")
        logger.info("   1. 重启Celery服务以应用新配置")
        logger.info("   2. 重启Web界面")
        logger.info("   3. 验证工作流正常运行")

        return 0

    except Exception as e:
        logger.error(f"❌ 统一化过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
