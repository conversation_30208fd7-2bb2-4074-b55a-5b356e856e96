# 千川自动化项目 - 铁律增强功能测试指南

**生成时间**: 2025-07-31  
**测试范围**: 铁律1和铁律2增强功能  
**测试目的**: 验证增强功能的实际效果

## 🎯 测试概述

本指南提供了详细的测试步骤，用于验证千川自动化项目的业务铁律增强功能是否正常工作。

## ✅ 代码审查结果

**语法检查**: 🎉 **全部通过**
- ✅ 上传可靠性增强工具语法正确
- ✅ 计划创建可靠性增强工具语法正确  
- ✅ 任务处理模块语法正确
- ✅ 调度器模块语法正确
- ✅ 计划创建模块语法正确

**修复的问题**:
- ✅ 导入路径问题已修复
- ✅ 日志库导入统一已完成
- ✅ 数据库连接导入错误已修复
- ✅ 变量名错误已修复

## 🧪 测试步骤

### 第一步：环境准备

```bash
# 1. 激活虚拟环境
conda activate qc_env

# 2. 确认配置文件设置
python -c "
import yaml
with open('config/settings.yml', 'r', encoding='utf-8') as f:
    config = yaml.safe_load(f)
robustness = config.get('robustness', {})
print('上传可靠性增强:', robustness.get('upload_reliability', {}).get('enabled', False))
print('计划创建可靠性增强:', robustness.get('plan_creation_reliability', {}).get('enabled', False))
"
```

### 第二步：基础功能测试

```bash
# 1. 测试增强工具导入
python -c "
try:
    from ai_tools.enhancement.ai_tool_20250731_enhancement_upload_reliability import UploadReliabilityEnhancer
    print('✅ 上传可靠性增强工具导入成功')
except Exception as e:
    print(f'❌ 上传可靠性增强工具导入失败: {e}')

try:
    from ai_tools.enhancement.ai_tool_20250731_enhancement_plan_creation_reliability import PlanCreationReliabilityEnhancer
    print('✅ 计划创建可靠性增强工具导入成功')
except Exception as e:
    print(f'❌ 计划创建可靠性增强工具导入失败: {e}')
"

# 2. 测试核心任务函数
python -c "
try:
    from qianchuan_aw.workflows.tasks import upload_single_video
    print('✅ 上传任务函数可用')
except Exception as e:
    print(f'❌ 上传任务函数不可用: {e}')

try:
    from qianchuan_aw.workflows.common.plan_creation import create_ad_plan_enhanced
    print('✅ 增强版计划创建函数可用')
except Exception as e:
    print(f'❌ 增强版计划创建函数不可用: {e}')
"
```

### 第三步：铁律1测试（视频上传100%成功率）

#### 3.1 启用上传可靠性增强

```yaml
# 修改 config/settings.yml
robustness:
  upload_reliability:
    enabled: true
    verification_enabled: true
    smart_retry_enabled: true
    alert_enabled: true
    max_retries: 5
    base_delay: 60
    exponential_backoff: true
```

#### 3.2 测试上传功能

```bash
# 1. 准备测试视频文件
# 确保有一个测试视频文件在 uploads/ 目录

# 2. 运行上传测试
python -c "
from qianchuan_aw.workflows.tasks import upload_single_video
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.db_utils import database_session

# 创建测试任务
with database_session() as db:
    # 查找一个待上传的素材
    creative = db.query(LocalCreative).filter(
        LocalCreative.status == 'pending_upload'
    ).first()
    
    if creative:
        print(f'找到测试素材: {creative.id}')
        # 这里可以手动触发上传任务进行测试
        print('可以通过Celery任务队列测试上传功能')
    else:
        print('没有找到待上传的素材，请先准备测试数据')
"
```

#### 3.3 监控上传结果

```bash
# 1. 查看上传日志
tail -f logs/qianchuan.log | grep -E "(上传|upload|重试|retry)"

# 2. 检查告警文件
ls -la alerts/upload_failures_*.json

# 3. 查看数据库状态
python -c "
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative

with database_session() as db:
    pending = db.query(LocalCreative).filter(LocalCreative.status == 'pending_upload').count()
    uploaded = db.query(LocalCreative).filter(LocalCreative.status == 'uploaded').count()
    failed = db.query(LocalCreative).filter(LocalCreative.status == 'upload_failed').count()
    
    print(f'待上传: {pending}')
    print(f'已上传: {uploaded}')
    print(f'上传失败: {failed}')
    
    if uploaded + failed > 0:
        success_rate = uploaded / (uploaded + failed) * 100
        print(f'上传成功率: {success_rate:.2f}%')
"
```

### 第四步：铁律2测试（计划创建100%成功率）

#### 4.1 启用计划创建可靠性增强

```yaml
# 修改 config/settings.yml
robustness:
  plan_creation_reliability:
    enabled: true
    verification_enabled: true
    smart_retry_enabled: true
    alert_enabled: true
    max_retries: 3
    base_delay: 30
    linear_backoff: true
```

#### 4.2 测试计划创建功能

```bash
# 1. 测试增强版计划创建
python -c "
from qianchuan_aw.workflows.common.plan_creation import create_ad_plan_enhanced
from qianchuan_aw.sdk_qc.client import QianchuanClient

# 这里需要实际的账户信息和计划参数
# 建议在测试环境中进行
print('增强版计划创建函数已准备就绪')
print('请在实际业务流程中测试计划创建功能')
"
```

#### 4.3 监控计划创建结果

```bash
# 1. 查看计划创建日志
tail -f logs/qianchuan.log | grep -E "(计划创建|plan_creation|重试|retry)"

# 2. 检查告警文件
ls -la alerts/plan_creation_failures_*.json

# 3. 查看计划创建统计
python -c "
# 这里可以添加计划创建统计的代码
print('请根据实际业务需求添加计划创建统计')
"
```

### 第五步：性能监控

#### 5.1 监控重试情况

```bash
# 查看重试日志
grep -E "(重试|retry)" logs/qianchuan.log | tail -20
```

#### 5.2 监控告警生成

```bash
# 检查告警文件
find alerts/ -name "*.json" -mtime -1 -exec echo "文件: {}" \; -exec cat {} \; -exec echo "" \;
```

#### 5.3 监控系统资源

```bash
# 监控CPU和内存使用
python -c "
import psutil
print(f'CPU使用率: {psutil.cpu_percent()}%')
print(f'内存使用率: {psutil.virtual_memory().percent}%')
"
```

## 📊 预期效果

### 铁律1（视频上传）预期改进：
- 🎯 **上传成功率**: 从当前水平提升到 **≥95%**
- 🔄 **智能重试**: 根据错误类型自动选择重试策略
- ⚡ **快速恢复**: 临时错误快速重试，永久错误立即停止
- 🚨 **及时告警**: 失败时立即生成告警文件

### 铁律2（计划创建）预期改进：
- 🎯 **创建成功率**: 从当前水平提升到 **≥95%**
- 🔍 **双重验证**: 通过API查询确认计划真正创建成功
- 🔄 **线性重试**: 避免API限流，稳定重试
- 📋 **状态跟踪**: 详细记录计划创建的每个步骤

## 🚨 故障排除

### 常见问题及解决方案：

1. **导入错误**
   ```bash
   # 检查Python路径
   python -c "import sys; print('\n'.join(sys.path))"
   ```

2. **配置未生效**
   ```bash
   # 重新加载配置
   python -c "
   import yaml
   with open('config/settings.yml', 'r', encoding='utf-8') as f:
       config = yaml.safe_load(f)
   print('配置加载成功')
   "
   ```

3. **数据库连接问题**
   ```bash
   # 测试数据库连接
   python -c "
   from qianchuan_aw.utils.db_utils import database_session
   with database_session() as db:
       print('数据库连接正常')
   "
   ```

## 📝 测试报告模板

测试完成后，请记录以下信息：

```
测试时间: ___________
测试环境: ___________

铁律1测试结果:
- 上传成功率: _____%
- 重试次数: _____次
- 告警生成: _____个
- 问题发现: _________

铁律2测试结果:
- 计划创建成功率: _____%
- 重试次数: _____次
- 告警生成: _____个
- 问题发现: _________

总体评价: ___________
建议改进: ___________
```

## 🎉 测试成功标准

- ✅ 所有基础功能测试通过
- ✅ 上传成功率显著提升
- ✅ 计划创建成功率显著提升
- ✅ 重试机制正常工作
- ✅ 告警系统正常工作
- ✅ 系统性能稳定

达到以上标准后，可以正式启用铁律增强功能！
