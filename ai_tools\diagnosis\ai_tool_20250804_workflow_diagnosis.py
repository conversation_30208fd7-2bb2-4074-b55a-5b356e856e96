#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 诊断工作流系统为什么不处理入库目录中的190个视频文件
清理条件: 成为项目核心诊断工具，长期保留
"""

import os
import sys
import subprocess
import psutil
from datetime import datetime, timedelta
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO"
    )

def check_input_directory():
    """检查入库目录状态"""
    logger.info("🔍 检查入库目录状态...")
    
    input_dir = "D:/workflow_assets/01_materials_to_process/缇萃百货"
    
    if not os.path.exists(input_dir):
        logger.error(f"❌ 入库目录不存在: {input_dir}")
        return 0, []
    
    # 获取所有mp4文件
    mp4_files = [f for f in os.listdir(input_dir) if f.endswith('.mp4')]
    file_count = len(mp4_files)
    
    logger.info(f"📁 入库目录: {input_dir}")
    logger.info(f"📄 视频文件数量: {file_count}")
    
    # 显示前10个文件作为示例
    if mp4_files:
        logger.info("📋 文件示例:")
        for i, filename in enumerate(sorted(mp4_files)[:10]):
            file_path = os.path.join(input_dir, filename)
            file_size = os.path.getsize(file_path) / (1024*1024)  # MB
            logger.info(f"   {i+1:2d}. {filename} ({file_size:.1f}MB)")
        
        if file_count > 10:
            logger.info(f"   ... 还有 {file_count - 10} 个文件")
    
    return file_count, mp4_files

def check_celery_processes():
    """检查Celery进程状态"""
    logger.info("🔍 检查Celery进程状态...")
    
    celery_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'celery' in cmdline.lower():
                    celery_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if celery_processes:
        logger.info(f"📊 发现 {len(celery_processes)} 个Celery进程:")
        for proc in celery_processes:
            logger.info(f"   PID {proc['pid']}: {proc['cmdline']}")
    else:
        logger.warning("⚠️ 未发现运行中的Celery进程")
    
    return celery_processes

def check_workflow_config():
    """检查工作流配置"""
    logger.info("🔍 检查工作流配置...")
    
    config_file = "config/settings.yml"
    
    if not os.path.exists(config_file):
        logger.error(f"❌ 配置文件不存在: {config_file}")
        return {}
    
    try:
        import yaml
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查关键配置
        workflow_config = config.get('workflow', {})
        file_ingestion = workflow_config.get('file_ingestion', {})
        
        logger.info("📋 工作流配置:")
        logger.info(f"   文件摄取启用: {file_ingestion.get('enabled', 'N/A')}")
        logger.info(f"   摄取间隔: {file_ingestion.get('interval_seconds', 'N/A')}秒")
        
        # 检查目录配置
        workflow_dirs = config.get('workflow_dirs', {})
        logger.info("📁 目录配置:")
        for key, value in workflow_dirs.items():
            logger.info(f"   {key}: {value}")
        
        return config
        
    except Exception as e:
        logger.error(f"❌ 读取配置文件失败: {e}")
        return {}

def check_database_file_status(sample_files):
    """检查数据库中文件状态"""
    logger.info("🔍 检查数据库中文件状态...")
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(
            host="localhost",
            database="qianchuan_postgres",
            user="postgres", 
            password="123456"
        )
        cur = conn.cursor()
        
        # 检查前10个文件在数据库中的状态
        sample_files_limited = sample_files[:10]
        
        if sample_files_limited:
            placeholders = ','.join(['%s'] * len(sample_files_limited))
            cur.execute(f"""
                SELECT filename, status, created_at, file_path
                FROM local_creatives 
                WHERE filename IN ({placeholders})
                ORDER BY created_at DESC
            """, sample_files_limited)
            
            db_records = cur.fetchall()
            
            logger.info(f"📊 数据库中的文件记录 ({len(db_records)}/{len(sample_files_limited)}):")
            
            if db_records:
                for filename, status, created_at, file_path in db_records:
                    logger.info(f"   📄 {filename}")
                    logger.info(f"      状态: {status}")
                    logger.info(f"      创建时间: {created_at}")
                    logger.info(f"      路径: {file_path}")
            else:
                logger.warning("⚠️ 数据库中没有找到这些文件的记录")
                logger.info("💡 这表明文件摄取任务可能没有运行")
        
        # 检查总体状态分布
        cur.execute("""
            SELECT status, COUNT(*) as count
            FROM local_creatives 
            GROUP BY status
            ORDER BY count DESC
        """)
        
        status_stats = cur.fetchall()
        
        logger.info("📊 数据库中所有文件状态分布:")
        for status, count in status_stats:
            logger.info(f"   {status}: {count} 个文件")
        
        cur.close()
        conn.close()
        
        return db_records, status_stats
        
    except Exception as e:
        logger.error(f"❌ 数据库查询失败: {e}")
        return [], []

def check_recent_logs():
    """检查最近的日志"""
    logger.info("🔍 检查最近的系统日志...")
    
    log_dirs = [
        "logs",
        ".",
        "src/qianchuan_aw"
    ]
    
    recent_logs = []
    
    for log_dir in log_dirs:
        if os.path.exists(log_dir):
            for file in os.listdir(log_dir):
                if file.endswith('.log'):
                    log_path = os.path.join(log_dir, file)
                    if os.path.isfile(log_path):
                        mtime = os.path.getmtime(log_path)
                        if datetime.now().timestamp() - mtime < 86400:  # 24小时内
                            recent_logs.append({
                                'path': log_path,
                                'mtime': datetime.fromtimestamp(mtime),
                                'size': os.path.getsize(log_path)
                            })
    
    if recent_logs:
        logger.info(f"📋 发现 {len(recent_logs)} 个最近的日志文件:")
        for log in sorted(recent_logs, key=lambda x: x['mtime'], reverse=True):
            logger.info(f"   📄 {log['path']} ({log['size']} bytes, {log['mtime']})")
    else:
        logger.warning("⚠️ 未发现最近的日志文件")
    
    return recent_logs

def test_manual_task_trigger():
    """测试手动触发任务"""
    logger.info("🔧 测试手动触发文件摄取任务...")
    
    try:
        # 尝试导入并手动执行文件摄取任务
        from src.qianchuan_aw.workflows import scheduler
        from src.qianchuan_aw.database.connection import database_session
        from src.qianchuan_aw.utils.config_loader import load_app_settings
        
        app_settings = load_app_settings()
        
        with database_session() as db:
            logger.info("🔄 手动执行文件摄取任务...")
            result = scheduler.handle_file_ingestion(db, app_settings)
            logger.success(f"✅ 文件摄取任务执行完成")
            return True
            
    except Exception as e:
        logger.error(f"❌ 手动触发任务失败: {e}")
        return False

def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 80)
    logger.info("🔧 工作流系统诊断 - 190个视频文件未处理问题")
    logger.info("=" * 80)
    
    # 1. 检查入库目录
    file_count, mp4_files = check_input_directory()
    
    # 2. 检查Celery进程
    celery_processes = check_celery_processes()
    
    # 3. 检查工作流配置
    config = check_workflow_config()
    
    # 4. 检查数据库状态
    db_records, status_stats = check_database_file_status(mp4_files)
    
    # 5. 检查最近日志
    recent_logs = check_recent_logs()
    
    # 6. 测试手动触发任务
    manual_trigger_success = test_manual_task_trigger()
    
    # 生成诊断报告
    logger.info("=" * 80)
    logger.info("🎯 诊断结果总结:")
    logger.info(f"   📁 入库目录文件: {file_count} 个")
    logger.info(f"   🔄 Celery进程: {len(celery_processes)} 个")
    logger.info(f"   💾 数据库记录: {len(db_records)} 个")
    logger.info(f"   📋 最近日志: {len(recent_logs)} 个")
    logger.info(f"   🔧 手动触发: {'成功' if manual_trigger_success else '失败'}")
    
    # 问题分析
    logger.info("\n🔍 问题分析:")
    
    if len(celery_processes) == 0:
        logger.error("❌ 关键问题: Celery调度器未运行")
        logger.info("💡 解决方案: 启动Celery Beat调度器")
        logger.info("   命令: celery -A src.qianchuan_aw.celery_app beat --loglevel=info")
    
    if len(db_records) == 0 and file_count > 0:
        logger.error("❌ 关键问题: 文件未被摄取到数据库")
        logger.info("💡 解决方案: 文件摄取任务未执行")
    
    if not manual_trigger_success:
        logger.error("❌ 关键问题: 手动触发任务失败")
        logger.info("💡 解决方案: 检查代码和配置")
    
    file_ingestion_enabled = config.get('workflow', {}).get('file_ingestion', {}).get('enabled', True)
    if not file_ingestion_enabled:
        logger.error("❌ 关键问题: 文件摄取功能被禁用")
        logger.info("💡 解决方案: 在配置文件中启用文件摄取")
    
    logger.info("=" * 80)

if __name__ == "__main__":
    main()
