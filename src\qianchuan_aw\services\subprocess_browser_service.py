# -*- coding: utf-8 -*-
"""
[V61.0] 子进程浏览器服务 - 彻底解决asyncio冲突

通过完全独立的子进程运行Playwright，彻底避免asyncio循环冲突。
这是解决Celery + Playwright冲突的最可靠方案。
"""
import os
import sys
import json
import time
import pickle
import multiprocessing
import atexit
from typing import Dict, Any, Optional, Tuple
from contextlib import contextmanager
from qianchuan_aw.utils.logger import logger

class BrowserWorkerProcess:
    """浏览器工作进程"""
    
    @staticmethod
    def worker_main(command_queue, result_queue, principal_name: str, app_settings: Dict[str, Any]):
        """工作进程主函数 - 在完全独立的进程中运行"""
        try:
            # 在子进程中，完全没有asyncio上下文
            import sys
            import os
            from pathlib import Path
            
            # 确保路径正确
            current_file_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.abspath(os.path.join(current_file_dir, '..', '..', '..'))
            src_path = os.path.join(project_root, 'src')
            if src_path not in sys.path:
                sys.path.insert(0, src_path)
            
            # 导入必要的模块
            from qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright
            
            # 初始化浏览器相关变量
            playwright_instance = None
            browser = None
            contexts = {}
            
            try:
                # 加载cookies
                cookies_paths = [
                    os.path.join(project_root, 'config', 'browser_cookies.json'),
                    os.path.join(project_root, '.auth', f'{principal_name}.auth.json')
                ]
                
                cookies_array = []
                cookies_found = False
                
                for cookies_path in cookies_paths:
                    if os.path.exists(cookies_path):
                        try:
                            with open(cookies_path, 'r', encoding='utf-8') as f:
                                cookies_data = json.load(f)
                            
                            if principal_name in str(cookies_path):
                                cookies_array = cookies_data.get('cookies', [])
                            elif isinstance(cookies_data, dict) and principal_name in cookies_data:
                                principal_data = cookies_data[principal_name]
                                cookies_array = principal_data.get('cookies', [])
                            
                            if cookies_array:
                                cookies_found = True
                                break
                                
                        except Exception as e:
                            continue
                
                if not cookies_found:
                    result_queue.put({'success': False, 'error': f"未找到主体 '{principal_name}' 的cookies"})
                    return
                
                cleaned_cookies = clean_cookies_for_playwright(cookies_array)
                if not cleaned_cookies:
                    result_queue.put({'success': False, 'error': "cookies清理后为空"})
                    return
                
                # 创建Playwright实例（在子进程中，绝对没有asyncio循环）
                from playwright.sync_api import sync_playwright
                
                playwright_instance = sync_playwright().start()
                browser = playwright_instance.chromium.launch(
                    headless=app_settings.get('browser', {}).get('headless', True),
                    args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-web-security']
                )
                
                # 通知主进程初始化成功
                result_queue.put({'success': True, 'message': 'Browser worker initialized'})
                
                # 进入命令处理循环
                idle_since = time.time()
                cfg = app_settings.get('browser_management', {}) if isinstance(app_settings, dict) else {}
                idle_timeout = int(cfg.get('worker_idle_timeout_seconds', int(os.environ.get('QC_BROWSER_WORKER_IDLE_TIMEOUT', '180'))))
                while True:
                    try:
                        # 等待命令
                        command = command_queue.get(timeout=60)  # 60秒超时

                        # 收到命令，重置空闲计时
                        idle_since = time.time()

                        if command['action'] == 'shutdown':
                            break
                        elif command['action'] == 'create_context':
                            # 创建浏览器上下文
                            context = browser.new_context(
                                viewport={'width': 1920, 'height': 1080},
                                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                            )
                            context.add_cookies(cleaned_cookies)

                            context_id = command['context_id']
                            contexts[context_id] = context

                            result_queue.put({'success': True, 'context_id': context_id})
                            
                        elif command['action'] == 'close_context':
                            # 关闭浏览器上下文
                            context_id = command['context_id']
                            if context_id in contexts:
                                contexts[context_id].close()
                                del contexts[context_id]
                                result_queue.put({'success': True})
                            else:
                                result_queue.put({'success': False, 'error': f'Context {context_id} not found'})
                                
                        elif command['action'] == 'execute_script':
                            # 执行JavaScript脚本
                            context_id = command['context_id']
                            script = command['script']
                            
                            if context_id in contexts:
                                context = contexts[context_id]
                                page = context.new_page()
                                try:
                                    result = page.evaluate(script)
                                    result_queue.put({'success': True, 'result': result})
                                except Exception as e:
                                    result_queue.put({'success': False, 'error': str(e)})
                                finally:
                                    page.close()
                            else:
                                result_queue.put({'success': False, 'error': f'Context {context_id} not found'})
                                
                        elif command['action'] == 'navigate_and_interact':
                            # 导航并执行交互操作
                            context_id = command['context_id']
                            url = command['url']
                            interactions = command['interactions']
                            
                            if context_id in contexts:
                                context = contexts[context_id]
                                page = context.new_page()
                                try:
                                    page.goto(url, wait_until="domcontentloaded")
                                    
                                    results = []
                                    for interaction in interactions:
                                        if interaction['type'] == 'click':
                                            page.locator(interaction['selector']).click()
                                        elif interaction['type'] == 'fill':
                                            page.locator(interaction['selector']).fill(interaction['text'])
                                        elif interaction['type'] == 'wait':
                                            page.wait_for_timeout(interaction['timeout'])
                                        elif interaction['type'] == 'get_text':
                                            text = page.locator(interaction['selector']).inner_text()
                                            results.append(text)
                                        elif interaction['type'] == 'get_title':
                                            title = page.title()
                                            results.append(title)
                                        elif interaction['type'] == 'screenshot':
                                            screenshot_path = interaction.get('path')
                                            full_page = interaction.get('full_page', False)
                                            if screenshot_path:
                                                page.screenshot(path=screenshot_path, full_page=full_page)
                                                results.append(screenshot_path)
                                            else:
                                                screenshot_bytes = page.screenshot(full_page=full_page)
                                                results.append(screenshot_bytes)
                                        elif interaction['type'] == 'press_key':
                                            page.keyboard.press(interaction['key'])
                                        elif interaction['type'] == 'evaluate':
                                            result = page.evaluate(interaction['script'])
                                            results.append(result)
                                    
                                    result_queue.put({'success': True, 'results': results})
                                except Exception as e:
                                    result_queue.put({'success': False, 'error': str(e)})
                                finally:
                                    page.close()
                            else:
                                result_queue.put({'success': False, 'error': f'Context {context_id} not found'})
                        
                    except multiprocessing.TimeoutError:
                        # 等待超时，检查是否空闲超时
                        if time.time() - idle_since >= idle_timeout:
                            result_queue.put({'success': True, 'message': f'Idle timeout {idle_timeout}s, exiting worker'})
                            break
                        continue
                    except Exception as e:
                        result_queue.put({'success': False, 'error': f'Command processing error: {str(e)}'})
                
            finally:
                # 清理资源
                for context in contexts.values():
                    try:
                        context.close()
                    except:
                        pass
                
                if browser:
                    try:
                        browser.close()
                    except:
                        pass
                
                if playwright_instance:
                    try:
                        playwright_instance.stop()
                    except:
                        pass
                
        except Exception as e:
            result_queue.put({'success': False, 'error': f'Worker process error: {str(e)}'})

class SubprocessBrowserService:
    """子进程浏览器服务管理器"""
    
    def __init__(self):
        self.process = None
        self.command_queue = None
        self.result_queue = None
        self.context_counter = 0
        self.is_initialized = False
    
    def start_worker(self, principal_name: str, app_settings: Dict[str, Any]) -> bool:
        """启动浏览器工作进程"""
        if self.process and self.process.is_alive():
            return True
        
        try:
            # 创建进程间通信队列
            self.command_queue = multiprocessing.Queue()
            self.result_queue = multiprocessing.Queue()
            
            # 启动工作进程
            self.process = multiprocessing.Process(
                target=BrowserWorkerProcess.worker_main,
                args=(self.command_queue, self.result_queue, principal_name, app_settings)
            )
            self.process.start()
            
            # 等待初始化完成
            try:
                result = self.result_queue.get(timeout=30)
                if result['success']:
                    self.is_initialized = True
                    logger.info("浏览器工作进程启动成功")
                    return True
                else:
                    logger.error(f"浏览器工作进程初始化失败: {result['error']}")
                    return False
            except:
                logger.error("浏览器工作进程初始化超时")
                return False
                
        except Exception as e:
            logger.error(f"启动浏览器工作进程失败: {e}")
            return False
    
    def create_context(self, timeout: int = 30) -> Optional[str]:
        """创建浏览器上下文"""
        if not self.is_initialized:
            return None
        
        try:
            self.context_counter += 1
            context_id = f"ctx_{self.context_counter}"
            
            command = {
                'action': 'create_context',
                'context_id': context_id
            }
            self.command_queue.put(command)
            
            result = self.result_queue.get(timeout=timeout)
            if result['success']:
                return context_id
            else:
                logger.error(f"创建浏览器上下文失败: {result.get('error', 'Unknown error')}")
                return None
                
        except Exception as e:
            logger.error(f"创建浏览器上下文异常: {e}")
            return None
    
    def close_context(self, context_id: str, timeout: int = 10) -> bool:
        """关闭浏览器上下文"""
        if not self.is_initialized or not context_id:
            return False
        
        try:
            command = {
                'action': 'close_context',
                'context_id': context_id
            }
            self.command_queue.put(command)
            
            result = self.result_queue.get(timeout=timeout)
            return result['success']
            
        except Exception as e:
            logger.error(f"关闭浏览器上下文异常: {e}")
            return False
    
    def navigate_and_interact(self, context_id: str, url: str, interactions: list, timeout: int = 60) -> Tuple[bool, Any]:
        """导航并执行交互操作"""
        if not self.is_initialized or not context_id:
            return False, "Service not initialized or invalid context"
        
        try:
            command = {
                'action': 'navigate_and_interact',
                'context_id': context_id,
                'url': url,
                'interactions': interactions
            }
            self.command_queue.put(command)
            
            result = self.result_queue.get(timeout=timeout)
            if result['success']:
                return True, result.get('results', [])
            else:
                return False, result.get('error', 'Unknown error')
                
        except Exception as e:
            logger.error(f"导航和交互操作异常: {e}")
            return False, str(e)
    
    def shutdown(self):
        """关闭浏览器服务"""
        if self.process and self.process.is_alive():
            try:
                self.command_queue.put({'action': 'shutdown'})
                self.process.join(timeout=10)
                if self.process.is_alive():
                    self.process.terminate()
                    self.process.join(timeout=5)
                    if self.process.is_alive():
                        self.process.kill()
            except:
                pass
        
        self.is_initialized = False
        logger.info("浏览器工作进程已关闭")

# 全局服务实例
_browser_service = None

def get_subprocess_browser_service(principal_name: str, app_settings: Dict[str, Any]) -> Optional[SubprocessBrowserService]:
    """获取子进程浏览器服务实例（带进程退出钩子自动清理）"""
    global _browser_service

    if _browser_service is None:
        _browser_service = SubprocessBrowserService()
        # 注册进程退出自动清理，防止残留子进程
        try:
            atexit.register(cleanup_subprocess_browser_service)
        except Exception:
            pass

    if not _browser_service.is_initialized:
        if not _browser_service.start_worker(principal_name, app_settings):
            return None

    return _browser_service

@contextmanager
def subprocess_browser_context(principal_name: str, app_settings: Dict[str, Any]):
    """子进程浏览器上下文管理器"""
    service = get_subprocess_browser_service(principal_name, app_settings)
    if not service:
        raise RuntimeError(f"无法为 '{principal_name}' 创建子进程浏览器服务")
    
    context_id = service.create_context()
    if not context_id:
        raise RuntimeError(f"无法创建浏览器上下文")
    
    try:
        # 返回一个包装对象，提供必要的接口
        yield SubprocessBrowserContext(service, context_id)
    finally:
        service.close_context(context_id)

class SubprocessBrowserContext:
    """子进程浏览器上下文包装器"""
    
    def __init__(self, service: SubprocessBrowserService, context_id: str):
        self.service = service
        self.context_id = context_id
    
    def navigate_and_interact(self, url: str, interactions: list) -> Tuple[bool, Any]:
        """导航并执行交互操作"""
        return self.service.navigate_and_interact(self.context_id, url, interactions)

def cleanup_subprocess_browser_service():
    """清理子进程浏览器服务"""
    global _browser_service
    if _browser_service:
        _browser_service.shutdown()
        _browser_service = None

# 兼容性适配器 - 让现有代码能够无缝迁移
class SubprocessBrowserAdapter:
    """子进程浏览器适配器 - 提供与原有浏览器服务兼容的接口"""

    def __init__(self, principal_name: str, app_settings: Dict[str, Any]):
        self.principal_name = principal_name
        self.app_settings = app_settings
        self.service = None
        self.context_id = None

    def __enter__(self):
        """进入上下文管理器"""
        self.service = get_subprocess_browser_service(self.principal_name, self.app_settings)
        if not self.service:
            raise RuntimeError(f"无法为 '{self.principal_name}' 创建子进程浏览器服务")

        self.context_id = self.service.create_context()
        if not self.context_id:
            raise RuntimeError(f"无法创建浏览器上下文")

        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        if self.service and self.context_id:
            self.service.close_context(self.context_id)

    def new_page(self):
        """创建新页面 - 返回页面适配器"""
        return SubprocessPageAdapter(self.service, self.context_id)

class SubprocessPageAdapter:
    """子进程页面适配器 - 提供与Playwright Page兼容的接口"""

    def __init__(self, service: SubprocessBrowserService, context_id: str):
        self.service = service
        self.context_id = context_id
        self.current_url = None
        self._default_timeout = 30000

    @property
    def url(self):
        """获取当前页面URL"""
        return self.current_url or ""

    def goto(self, url: str, wait_until: str = "domcontentloaded"):
        """导航到URL"""
        self.current_url = url
        interactions = [{'type': 'wait', 'timeout': 3000}]  # 等待页面加载
        success, result = self.service.navigate_and_interact(self.context_id, url, interactions)
        if not success:
            raise Exception(f"导航到 {url} 失败: {result}")

    def click(self, selector: str, timeout: int = 30000):
        """点击元素"""
        if not self.current_url:
            raise Exception("页面未导航到任何URL")

        interactions = [{'type': 'click', 'selector': selector}]
        success, result = self.service.navigate_and_interact(self.context_id, self.current_url, interactions)
        if not success:
            raise Exception(f"点击 {selector} 失败: {result}")

    def fill(self, selector: str, text: str):
        """填充文本"""
        if not self.current_url:
            raise Exception("页面未导航到任何URL")

        interactions = [{'type': 'fill', 'selector': selector, 'text': text}]
        success, result = self.service.navigate_and_interact(self.context_id, self.current_url, interactions)
        if not success:
            raise Exception(f"填充 {selector} 失败: {result}")

    def inner_text(self, selector: str) -> str:
        """获取元素文本"""
        if not self.current_url:
            raise Exception("页面未导航到任何URL")

        interactions = [{'type': 'get_text', 'selector': selector}]
        success, result = self.service.navigate_and_interact(self.context_id, self.current_url, interactions)
        if not success:
            raise Exception(f"获取 {selector} 文本失败: {result}")

        return result[0] if result else ""

    def wait_for_timeout(self, timeout: int):
        """等待指定时间"""
        if not self.current_url:
            raise Exception("页面未导航到任何URL")

        interactions = [{'type': 'wait', 'timeout': timeout}]
        success, result = self.service.navigate_and_interact(self.context_id, self.current_url, interactions)
        if not success:
            raise Exception(f"等待超时失败: {result}")

    def locator(self, selector: str):
        """返回定位器适配器"""
        return SubprocessLocatorAdapter(self, selector)

    def get_by_text(self, text: str):
        """通过文本获取元素"""
        selector = f"text={text}"
        return SubprocessLocatorAdapter(self, selector)

    def get_by_role(self, role: str, name: str = None):
        """通过角色获取元素"""
        if name:
            selector = f"[role='{role}'][name='{name}']"
        else:
            selector = f"[role='{role}']"
        return SubprocessLocatorAdapter(self, selector)

    def press(self, key: str):
        """按键"""
        if not self.current_url:
            raise Exception("页面未导航到任何URL")

        interactions = [{'type': 'press_key', 'key': key}]
        success, result = self.service.navigate_and_interact(self.context_id, self.current_url, interactions)
        if not success:
            raise Exception(f"按键 {key} 失败: {result}")

    def evaluate(self, script: str):
        """执行JavaScript"""
        if not self.current_url:
            raise Exception("页面未导航到任何URL")

        interactions = [{'type': 'evaluate', 'script': script}]
        success, result = self.service.navigate_and_interact(self.context_id, self.current_url, interactions)
        if not success:
            raise Exception(f"执行脚本失败: {result}")

        return result[0] if result else None

    def set_default_timeout(self, timeout: int):
        """设置默认超时时间"""
        self._default_timeout = timeout

    def title(self) -> str:
        """获取页面标题"""
        if not self.current_url:
            return ""

        interactions = [{'type': 'get_title'}]
        success, result = self.service.navigate_and_interact(self.context_id, self.current_url, interactions)
        if success and result:
            return result[0]
        return ""

    def screenshot(self, path: str = None, full_page: bool = False):
        """截图功能"""
        if not self.current_url:
            raise Exception("页面未导航到任何URL")

        interactions = [{'type': 'screenshot', 'path': path, 'full_page': full_page}]
        success, result = self.service.navigate_and_interact(self.context_id, self.current_url, interactions)
        if not success:
            raise Exception(f"截图失败: {result}")

        return result[0] if result else None

class SubprocessLocatorAdapter:
    """子进程定位器适配器"""

    def __init__(self, page: SubprocessPageAdapter, selector: str):
        self.page = page
        self.selector = selector

    def click(self, timeout: int = 30000):
        """点击元素"""
        self.page.click(self.selector, timeout)

    def fill(self, text: str):
        """填充文本"""
        self.page.fill(self.selector, text)

    def inner_text(self) -> str:
        """获取元素文本"""
        return self.page.inner_text(self.selector)

    def first(self):
        """获取第一个匹配的元素"""
        # 修改选择器以获取第一个元素
        if "text=" in self.selector:
            return self
        else:
            new_selector = f"({self.selector}):first-child"
            return SubprocessLocatorAdapter(self.page, new_selector)

    def nth(self, index: int):
        """获取第n个匹配的元素"""
        # 修改选择器以包含索引
        if "text=" in self.selector:
            # 对于文本选择器，暂时返回自身
            return self
        else:
            # 对于CSS选择器，添加nth-child
            new_selector = f"({self.selector}):nth-child({index + 1})"
            return SubprocessLocatorAdapter(self.page, new_selector)

    def all(self):
        """获取所有匹配的元素"""
        # 返回一个包含自身的列表，模拟多个元素
        return [self]

    def is_visible(self, timeout: int = 5000) -> bool:
        """检查元素是否可见"""
        try:
            # 尝试获取元素文本，如果成功则认为可见
            self.page.inner_text(self.selector)
            return True
        except:
            return False

    def press(self, key: str):
        """在元素上按键"""
        # 先点击元素获得焦点，然后按键
        self.click()
        self.page.press(key)

    def clear(self):
        """清空元素内容"""
        # 选择所有内容并删除
        self.click()
        self.page.press("Control+a")
        self.page.press("Delete")

# 提供兼容的上下文管理器
@contextmanager
def subprocess_managed_browser_context(principal_name: str, app_settings: Dict[str, Any]):
    """子进程管理的浏览器上下文 - 兼容原有接口"""
    adapter = SubprocessBrowserAdapter(principal_name, app_settings)
    try:
        yield adapter.__enter__()
    finally:
        adapter.__exit__(None, None, None)
