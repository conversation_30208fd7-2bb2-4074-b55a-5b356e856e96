#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 修复千川自动化项目中违反业务铁律的问题
清理条件: 成为项目永久维护工具，不删除
"""

import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from loguru import logger
from datetime import datetime, timezone, timedelta
from typing import Tuple

def _can_safely_retry_appeal(plan) -> Tuple[bool, str]:
    """
    检查是否可以安全地重试提审
    
    这是业务铁律的核心检查函数，确保绝对不会重复提审已成功提审的计划
    
    Args:
        plan: Campaign对象
        
    Returns:
        Tuple[bool, str]: (是否可以安全重试, 原因说明)
    """
    
    # 🛡️ 检查1: 提审次数限制 - 严格的一次性原则
    if plan.appeal_attempt_count and plan.appeal_attempt_count > 0:
        return False, f"计划已提审过 {plan.appeal_attempt_count} 次，严格遵循一次性原则"
    
    # 🛡️ 检查2: 提审时间检查 - 有提审历史就不允许重试
    if plan.first_appeal_at:
        return False, f"计划已有提审历史 (首次提审: {plan.first_appeal_at})，不允许重复提审"
    
    # 🛡️ 检查3: 申诉状态检查 - 避免状态冲突
    if plan.appeal_status in ['appeal_pending', 'appealing']:
        return False, f"计划申诉状态为 {plan.appeal_status}，不允许重复提审"
    
    # 🛡️ 检查4: 最后提审时间检查 - 防止短时间内重复操作
    if plan.last_appeal_at:
        time_since_last_appeal = datetime.now(timezone.utc) - plan.last_appeal_at
        if time_since_last_appeal < timedelta(hours=1):
            return False, f"距离上次提审仅 {time_since_last_appeal}，时间间隔过短"
    
    return True, "可以安全提审"

def generate_fixed_scheduler_code():
    """生成修复后的调度器代码片段"""
    
    logger.info("🔧 生成修复后的调度器代码")
    
    # 修复1: NO_RECORD 处理逻辑 (监控阶段)
    monitoring_fix = '''
        elif appeal_status == 'NO_RECORD':
            # 🛡️ 业务铁律检查 - 防止重复提审
            can_retry, reason = _can_safely_retry_appeal(plan)
            if not can_retry:
                logger.warning(f"🛡️ [业务铁律] 计划 {plan.campaign_id_qc} 检测到NO_RECORD但{reason}，跳过重新提审")
                continue
            
            # 额外保险: 检查计划年龄，避免对新计划误判
            if plan.created_at and (datetime.now(timezone.utc) - plan.created_at) < timedelta(hours=2):
                logger.info(f"计划 {plan.campaign_id_qc} 创建时间较短，NO_RECORD可能是正常延迟，等待下轮检查")
                continue
            
            logger.error(f"❌ 计划 {plan.campaign_id_qc} 确认无申诉记录且符合重试条件，执行保守重新提审...")
            from qianchuan_aw.services.appeal_browser_service import perform_appeal_via_browser
            browser_success, _ = perform_appeal_via_browser(principal.name, account.account_id_qc, plan.campaign_id_qc, app_settings)
            if browser_success:
                logger.success(f"🛡️ [业务铁律] 保守重新提审计划 {plan.campaign_id_qc} 成功，更新提审计数")
                # 🛡️ 更新提审计数，确保不会再次重试
                plan.appeal_attempt_count = (plan.appeal_attempt_count or 0) + 1
                plan.first_appeal_at = datetime.now(timezone.utc)
                plan.last_appeal_at = datetime.now(timezone.utc)
                db.commit()
            else:
                logger.error(f"保守重新提审计划 {plan.campaign_id_qc} 仍然失败")
    '''
    
    # 修复2: 事实核查阶段的 NO_RECORD 处理
    verification_fix = '''
        elif verification_result == 'NO_RECORD':
            # 🛡️ 业务铁律检查 - 防止重复提审
            can_retry, reason = _can_safely_retry_appeal(plan)
            if not can_retry:
                logger.warning(f"🛡️ [业务铁律] 计划 {plan.campaign_id_qc} 事实核查发现NO_RECORD但{reason}，跳过重新提审")
                continue
            
            logger.error(f"❌ 事实核查失败！计划 {plan.campaign_id_qc} 在后台无申诉记录，执行保守重新提审...")
            from qianchuan_aw.services.appeal_browser_service import perform_appeal_via_browser
            browser_success, _ = perform_appeal_via_browser(principal.name, account.account_id_qc, plan.campaign_id_qc, app_settings)
            if browser_success: 
                logger.success(f"🛡️ [业务铁律] 事实核查阶段保守重新提审计划 {plan.campaign_id_qc} 成功")
                # 🛡️ 更新提审计数
                plan.appeal_attempt_count = (plan.appeal_attempt_count or 0) + 1
                plan.first_appeal_at = plan.first_appeal_at or datetime.now(timezone.utc)
                plan.last_appeal_at = datetime.now(timezone.utc)
                db.commit()
    '''
    
    # 修复3: 避免重复收割已完成计划
    completed_plan_fix = '''
        if plan.status == 'COMPLETED':
            logger.info(f"🛡️ [业务铁律] 计划 {plan.campaign_id_qc} 已完成，跳过重复收割")
            continue
    '''
    
    logger.info("✅ 修复代码生成完成")
    
    return {
        'monitoring_fix': monitoring_fix,
        'verification_fix': verification_fix,
        'completed_plan_fix': completed_plan_fix
    }

def validate_business_rules_compliance():
    """验证业务铁律合规性"""
    
    logger.info("🔍 验证业务铁律合规性")
    
    compliance_report = {
        'rule_1_file_deletion': True,  # 删除文件逻辑
        'rule_2_plan_completion': True,  # 计划完成状态
        'rule_3_no_duplicate_appeal': False,  # 防重复提审 - 当前不合规
        'violations_found': [],
        'recommendations': []
    }
    
    # 检查违规点
    violations = [
        {
            'location': 'scheduler.py:1666-1677',
            'type': '重复提审风险',
            'description': 'NO_RECORD状态处理缺少appeal_attempt_count检查',
            'severity': '高危',
            'impact': '可能导致已成功提审的计划被重复提审'
        },
        {
            'location': 'scheduler.py:1496-1500', 
            'type': '重复提审风险',
            'description': '事实核查失败后重新提审缺少安全检查',
            'severity': '高危',
            'impact': '违反一次性提审原则'
        },
        {
            'location': 'scheduler.py:1420',
            'type': '重复收割问题',
            'description': '已完成计划仍被重复收割',
            'severity': '中等',
            'impact': '产生无意义的警告日志，浪费资源'
        }
    ]
    
    compliance_report['violations_found'] = violations
    
    # 生成建议
    recommendations = [
        "立即添加 _can_safely_retry_appeal 函数进行严格检查",
        "修复所有 NO_RECORD 状态的处理逻辑",
        "避免对已完成计划的重复操作",
        "增强日志记录，追踪关键决策过程",
        "定期审计检查，确保持续合规"
    ]
    
    compliance_report['recommendations'] = recommendations
    
    logger.warning(f"⚠️ 发现 {len(violations)} 个业务铁律违规问题")
    for violation in violations:
        logger.error(f"❌ {violation['location']}: {violation['description']} (严重程度: {violation['severity']})")
    
    logger.info("📋 合规性建议:")
    for i, rec in enumerate(recommendations, 1):
        logger.info(f"   {i}. {rec}")
    
    return compliance_report

def create_implementation_plan():
    """创建实施计划"""
    
    logger.info("📋 创建业务铁律修复实施计划")
    
    plan = {
        'phase_1_immediate': [
            "添加 _can_safely_retry_appeal 安全检查函数",
            "修复监控阶段的 NO_RECORD 处理逻辑",
            "修复事实核查阶段的 NO_RECORD 处理逻辑", 
            "避免已完成计划的重复收割"
        ],
        'phase_2_enhancement': [
            "增强日志记录和监控告警",
            "添加业务铁律合规性检查",
            "创建定期审计机制",
            "完善错误处理和回滚机制"
        ],
        'phase_3_validation': [
            "全面测试修复后的逻辑",
            "验证不再有重复提审情况",
            "确认系统性能和稳定性",
            "更新文档和操作手册"
        ]
    }
    
    logger.info("🎯 第一阶段 (立即执行):")
    for task in plan['phase_1_immediate']:
        logger.info(f"   • {task}")
    
    logger.info("📈 第二阶段 (后续优化):")
    for task in plan['phase_2_enhancement']:
        logger.info(f"   • {task}")
    
    logger.info("✅ 第三阶段 (验证确认):")
    for task in plan['phase_3_validation']:
        logger.info(f"   • {task}")
    
    return plan

if __name__ == "__main__":
    logger.info("🚀 启动业务铁律违规修复工具")
    
    try:
        # 1. 验证当前合规性
        compliance_report = validate_business_rules_compliance()
        
        # 2. 生成修复代码
        fix_codes = generate_fixed_scheduler_code()
        
        # 3. 创建实施计划
        implementation_plan = create_implementation_plan()
        
        logger.info("✅ 业务铁律违规分析和修复方案生成完成")
        
        logger.warning("🚨 关键提醒:")
        logger.warning("   1. 当前系统存在严重的重复提审风险")
        logger.warning("   2. 必须立即修复以避免违反业务铁律")
        logger.warning("   3. 修复后需要全面测试验证")
        
        logger.info("📁 相关文件:")
        logger.info("   • 详细分析报告: ai_reports/ai_report_20250803_audit_business_rules_violations.md")
        logger.info("   • 修复代码: 本工具生成的代码片段")
        logger.info("   • 实施计划: 分阶段执行修复")
        
    except Exception as e:
        logger.error(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
