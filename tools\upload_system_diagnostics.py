#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上传系统诊断工具 - 核心工具
全面检查和诊断上传系统的健康状态
"""

import argparse
import os
import sys
import time
from pathlib import Path
from typing import List, Dict, Any

# --- 路径设置 ---
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.config_manager import get_config_manager
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import AdAccount, Principal, LocalCreative, PlatformCreative
from qianchuan_aw.workflows.batch_uploader import BatchUploader
from qianchuan_aw.utils.file_utils import get_file_md5
from qianchuan_aw.utils.workflow_helpers import find_or_create_local_creative


class UploadSystemDiagnostics:
    """上传系统诊断器"""
    
    def __init__(self):
        """初始化诊断器"""
        self.config_manager = get_config_manager()
        self.app_settings = self.config_manager.get_config()
        self.diagnostic_results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'tests': [],
            'issues': [],
            'recommendations': []
        }
    
    def test_database_connectivity(self) -> bool:
        """测试数据库连接"""
        logger.info("🔍 测试数据库连接...")
        
        try:
            with database_session() as db:
                # 测试基本查询
                principal_count = db.query(Principal).count()
                account_count = db.query(AdAccount).count()
                
                logger.info(f"✅ 数据库连接正常")
                logger.info(f"   主体数量: {principal_count}")
                logger.info(f"   账户数量: {account_count}")
                
                if principal_count == 0:
                    self.diagnostic_results['issues'].append("数据库中没有主体记录")
                    return False
                
                if account_count == 0:
                    self.diagnostic_results['issues'].append("数据库中没有账户记录")
                    return False
                
                return True
                
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            self.diagnostic_results['issues'].append(f"数据库连接失败: {e}")
            return False
    
    def test_batch_uploader_initialization(self) -> bool:
        """测试批量上传器初始化"""
        logger.info("🔍 测试批量上传器初始化...")
        
        try:
            batch_uploader = BatchUploader(self.app_settings)
            
            logger.info(f"✅ 批量上传器初始化成功")
            logger.info(f"   并发数: {batch_uploader.max_workers}")
            logger.info(f"   批次大小: {batch_uploader.batch_size}")
            logger.info(f"   MD5缓存: {batch_uploader.enable_md5_cache}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 批量上传器初始化失败: {e}")
            self.diagnostic_results['issues'].append(f"批量上传器初始化失败: {e}")
            return False
    
    def test_task_preparation(self) -> bool:
        """测试任务准备流程"""
        logger.info("🔍 测试任务准备流程...")
        
        try:
            with database_session() as db:
                # 获取测试数据
                principal = db.query(Principal).first()
                account = db.query(AdAccount).first()
                
                if not principal or not account:
                    logger.error("❌ 缺少测试数据 (主体或账户)")
                    self.diagnostic_results['issues'].append("缺少测试数据")
                    return False
                
                # 创建模拟文件路径
                mock_file_path = "/mock/test_video.mp4"
                mock_md5 = "mock_md5_hash_for_testing"
                
                # 测试本地素材创建
                local_creative = find_or_create_local_creative(db, principal.id, mock_md5, mock_file_path)
                
                if not local_creative or not local_creative.id:
                    logger.error("❌ 本地素材创建失败")
                    self.diagnostic_results['issues'].append("本地素材创建失败")
                    return False
                
                # 测试任务数据完整性
                task_data = {
                    'file_path': mock_file_path,
                    'local_creative_id': local_creative.id,
                    'account_id': account.id,
                    'principal_name': principal.name
                }
                
                required_fields = ['file_path', 'local_creative_id', 'account_id', 'principal_name']
                for field in required_fields:
                    if field not in task_data or task_data[field] is None:
                        logger.error(f"❌ 任务数据缺少字段: {field}")
                        self.diagnostic_results['issues'].append(f"任务数据缺少字段: {field}")
                        return False
                
                logger.info("✅ 任务准备流程正常")
                logger.info(f"   测试主体: {principal.name}")
                logger.info(f"   测试账户: {account.name}")
                logger.info(f"   本地素材ID: {local_creative.id}")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ 任务准备测试失败: {e}")
            self.diagnostic_results['issues'].append(f"任务准备测试失败: {e}")
            return False
    
    def test_api_credentials(self) -> bool:
        """测试API凭证"""
        logger.info("🔍 测试API凭证...")
        
        try:
            api_credentials = self.config_manager.get_api_credentials()
            
            required_creds = ['app_id', 'secret']
            for cred in required_creds:
                if cred not in api_credentials or not api_credentials[cred]:
                    logger.error(f"❌ 缺少API凭证: {cred}")
                    self.diagnostic_results['issues'].append(f"缺少API凭证: {cred}")
                    return False
            
            logger.info("✅ API凭证配置正常")
            logger.info(f"   App ID: {api_credentials['app_id'][:8]}...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ API凭证测试失败: {e}")
            self.diagnostic_results['issues'].append(f"API凭证测试失败: {e}")
            return False
    
    def test_configuration_validity(self) -> bool:
        """测试配置有效性"""
        logger.info("🔍 测试配置有效性...")
        
        try:
            # 检查关键配置
            workflow_config = self.app_settings.get('workflow', {})
            db_config = self.app_settings.get('database', {})
            upload_config = self.app_settings.get('upload_optimization', {})
            
            # 检查并发配置
            max_workers = workflow_config.get('max_upload_workers', 0)
            if max_workers <= 0:
                self.diagnostic_results['issues'].append("并发工作线程数配置无效")
                return False
            
            # 检查数据库连接池
            pool_config = db_config.get('connection_pool', {})
            pool_size = pool_config.get('pool_size', 0)
            max_overflow = pool_config.get('max_overflow', 0)
            
            if pool_size <= 0:
                self.diagnostic_results['issues'].append("数据库连接池大小配置无效")
                return False
            
            # 检查配置合理性
            total_connections = pool_size + max_overflow
            if max_workers * 3 > total_connections:
                self.diagnostic_results['recommendations'].append(
                    f"建议增加数据库连接池大小: 当前{total_connections}, 建议>{max_workers * 3}"
                )
            
            logger.info("✅ 配置有效性检查通过")
            logger.info(f"   并发线程: {max_workers}")
            logger.info(f"   数据库连接池: {pool_size} + {max_overflow}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置有效性测试失败: {e}")
            self.diagnostic_results['issues'].append(f"配置有效性测试失败: {e}")
            return False
    
    def test_file_operations(self) -> bool:
        """测试文件操作"""
        logger.info("🔍 测试文件操作...")
        
        try:
            # 创建临时测试文件
            test_file = Path(project_root) / 'ai_temp' / 'test_upload_file.txt'
            test_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(test_file, 'w') as f:
                f.write("test content for upload diagnostics")
            
            # 测试MD5计算
            md5_hash = get_file_md5(str(test_file))
            if not md5_hash:
                logger.error("❌ MD5计算失败")
                self.diagnostic_results['issues'].append("MD5计算功能异常")
                return False
            
            logger.info("✅ 文件操作正常")
            logger.info(f"   测试文件MD5: {md5_hash}")
            
            # 清理测试文件
            test_file.unlink()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 文件操作测试失败: {e}")
            self.diagnostic_results['issues'].append(f"文件操作测试失败: {e}")
            return False
    
    def run_comprehensive_diagnostics(self):
        """运行综合诊断"""
        logger.info("🎯 千川自动化 - 上传系统诊断")
        logger.info("📌 目标: 全面检查上传系统健康状态")
        logger.info("="*60)
        
        tests = [
            ("数据库连接", self.test_database_connectivity),
            ("批量上传器初始化", self.test_batch_uploader_initialization),
            ("任务准备流程", self.test_task_preparation),
            ("API凭证", self.test_api_credentials),
            ("配置有效性", self.test_configuration_validity),
            ("文件操作", self.test_file_operations)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔍 {test_name}")
            try:
                result = test_func()
                if result:
                    passed_tests += 1
                    self.diagnostic_results['tests'].append({'name': test_name, 'result': 'PASS'})
                else:
                    self.diagnostic_results['tests'].append({'name': test_name, 'result': 'FAIL'})
            except Exception as e:
                logger.error(f"❌ 测试执行异常: {e}")
                self.diagnostic_results['tests'].append({'name': test_name, 'result': 'ERROR'})
        
        # 生成诊断报告
        print("\n" + "="*60)
        print("📊 诊断结果总结:")
        print(f"  ✅ 通过测试: {passed_tests}/{total_tests}")
        print(f"  ❌ 失败测试: {total_tests - passed_tests}/{total_tests}")
        
        if self.diagnostic_results['issues']:
            print(f"\n⚠️ 发现问题 ({len(self.diagnostic_results['issues'])} 个):")
            for i, issue in enumerate(self.diagnostic_results['issues'], 1):
                print(f"  {i}. {issue}")
        
        if self.diagnostic_results['recommendations']:
            print(f"\n💡 优化建议 ({len(self.diagnostic_results['recommendations'])} 个):")
            for i, rec in enumerate(self.diagnostic_results['recommendations'], 1):
                print(f"  {i}. {rec}")
        
        if passed_tests == total_tests:
            print("\n🎉 所有诊断测试通过！上传系统健康状态良好")
            print("💡 建议: 可以正常使用批量上传功能")
        else:
            print("\n⚠️ 发现问题，需要修复后再使用上传功能")
            print("💡 建议: 根据问题列表逐项修复")
        
        return passed_tests == total_tests


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='上传系统诊断工具')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    try:
        diagnostics = UploadSystemDiagnostics()
        success = diagnostics.run_comprehensive_diagnostics()
        
        if success:
            print("\n🚀 诊断完成！系统状态良好")
        else:
            print("\n❌ 诊断发现问题，需要修复")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断诊断")
    except Exception as e:
        logger.error(f"诊断执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
