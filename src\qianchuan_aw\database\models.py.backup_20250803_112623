# -*- coding: utf-8 -*-
"""
@Project: qianchuangzl
@File   : models.py
<AUTHOR> AI Assistant
@Date   : 2025/6/17
@Desc   : 业务流相关的数据库模型定义
"""
from datetime import datetime
from typing import List, Optional

from sqlalchemy import (create_engine, Column, Integer, String, DateTime, ForeignKey,
                        Float, Table, UniqueConstraint, Boolean, Index, func, Text, BigInteger)
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, relationship

class Base(DeclarativeBase):
    pass

# 多对多关联表：广告计划(Campaign) <-> 平台素材(PlatformCreative)
campaign_platform_creative_association = Table(
    'campaign_platform_creative_association',
    Base.metadata,
    Column('campaign_id', Integer, ForeignKey('campaigns.id'), primary_key=True),
    Column('platform_creative_id', Integer, ForeignKey('platform_creatives.id'), primary_key=True)
)

class Principal(Base):
    """主体表"""
    __tablename__ = 'principals'
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String, unique=True, index=True)
    cc_account_id: Mapped[Optional[int]] = mapped_column(BigInteger, unique=True, index=True, nullable=True, comment="千川纵横工作台ID")
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
 
    ad_accounts: Mapped[List["AdAccount"]] = relationship(back_populates="principal")
    local_creatives: Mapped[List["LocalCreative"]] = relationship(back_populates="principal")

class AdAccount(Base):
    """广告账户表"""
    __tablename__ = 'ad_accounts'
    id: Mapped[int] = mapped_column(primary_key=True)
    principal_id: Mapped[int] = mapped_column(ForeignKey('principals.id'))
    account_id_qc: Mapped[str] = mapped_column(String, unique=True, index=True)
    name: Mapped[str] = mapped_column(String)
    aweme_id: Mapped[Optional[str]] = mapped_column(String, nullable=True, comment="关联的抖音号UID")
    account_type: Mapped[str] = mapped_column(String, default='UNSET', comment="账户类型 (TEST, DELIVERY, UNSET)")
    status: Mapped[str] = mapped_column(String, default='active', index=True, comment="账户状态 (active, disabled, temporarily_blocked)")
    blocked_until: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True, comment="账户临时封禁截止时间")
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
 
    principal: Mapped["Principal"] = relationship(back_populates="ad_accounts")
    platform_creatives: Mapped[List["PlatformCreative"]] = relationship(back_populates="account")
    campaigns: Mapped[List["Campaign"]] = relationship(back_populates="account")
    performance_snapshots: Mapped[List["CreativePerformanceSnapshot"]] = relationship(back_populates="account")
    is_test_account: Mapped[bool] = mapped_column(Boolean, default=False, index=True, comment="[V62.0] 是否为测试账户")
    is_favorite: Mapped[bool] = mapped_column(Boolean, default=False, index=True, comment="[V63.0] 是否为收藏账户，用于快速访问常用账户")


class LocalCreative(Base):
    """本地素材表"""
    __tablename__ = 'local_creatives'
    id: Mapped[int] = mapped_column(primary_key=True)
    principal_id: Mapped[int] = mapped_column(ForeignKey('principals.id'))
    file_path: Mapped[str] = mapped_column(String, nullable=True)
    filename: Mapped[Optional[str]] = mapped_column(String, index=True, comment="文件名，方便查询和关联")
    file_hash: Mapped[str] = mapped_column(String, unique=True, index=True)
    status: Mapped[str] = mapped_column(String, default='new', index=True)
    similarity_group: Mapped[Optional[str]] = mapped_column(String, index=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")

    uploaded_to_account_id: Mapped[Optional[int]] = mapped_column(ForeignKey('ad_accounts.id'))
    video_id: Mapped[Optional[str]] = mapped_column(String)
    material_id_qc: Mapped[Optional[str]] = mapped_column(String)

    # 收割相关字段
    harvest_status: Mapped[str] = mapped_column(String(50), default='not_harvested', comment="收割状态")
    harvest_attempt_count: Mapped[int] = mapped_column(Integer, default=0, comment="收割尝试次数")
    last_harvest_attempt: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="最后收割尝试时间")
    harvest_lock_key: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, comment="收割锁定键")
    review_checked_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="审核检查时间")

    principal: Mapped["Principal"] = relationship(back_populates="local_creatives")
    uploaded_to_account: Mapped[Optional["AdAccount"]] = relationship(foreign_keys=[uploaded_to_account_id])
    platform_creatives: Mapped[List["PlatformCreative"]] = relationship(back_populates="local_creative")

class PlatformCreative(Base):
    """平台素材与账户的关联表"""
    __tablename__ = 'platform_creatives'
    id: Mapped[int] = mapped_column(primary_key=True)
    local_creative_id: Mapped[int] = mapped_column(ForeignKey('local_creatives.id'))
    account_id: Mapped[int] = mapped_column(ForeignKey('ad_accounts.id'))
    
    material_id_qc: Mapped[str] = mapped_column(String, index=True)
    video_id: Mapped[str] = mapped_column(String)
    video_url: Mapped[Optional[str]] = mapped_column(String)
    video_cover_id: Mapped[str] = mapped_column(String)
    
    review_status: Mapped[str] = mapped_column(String, default='pending')
    promotion_status: Mapped[str] = mapped_column(String, default='pending')
    last_checked: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    local_creative: Mapped["LocalCreative"] = relationship(back_populates="platform_creatives")
    account: Mapped["AdAccount"] = relationship(back_populates="platform_creatives")
    campaigns: Mapped[List["Campaign"]] = relationship(secondary=campaign_platform_creative_association, back_populates="platform_creatives")
    performance_snapshots: Mapped[List["CreativePerformanceSnapshot"]] = relationship(back_populates="platform_creative")

class AuthCredential(Base):
    """存储每个主体的API认证信息"""
    __tablename__ = 'auth_credentials'
    id: Mapped[int] = mapped_column(primary_key=True)
    principal_id: Mapped[int] = mapped_column(ForeignKey('principals.id'), unique=True, index=True)
    access_token: Mapped[str] = mapped_column(String(512))
    refresh_token: Mapped[str] = mapped_column(String(512))
    token_expires_at: Mapped[int] = mapped_column(Integer)
    refresh_token_expires_at: Mapped[int] = mapped_column(Integer)

    principal: Mapped["Principal"] = relationship()


class Campaign(Base):
    """广告计划表"""
    __tablename__ = 'campaigns'
    id: Mapped[int] = mapped_column(primary_key=True)
    campaign_id_qc: Mapped[str] = mapped_column(String, unique=True, index=True)
    account_id: Mapped[int] = mapped_column(ForeignKey('ad_accounts.id'))
    
    status: Mapped[str] = mapped_column(String, default="UNKNOWN", index=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=datetime.now)
    last_appeal_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))
    first_appeal_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))

    spend: Mapped[Optional[float]] = mapped_column(Float)
    last_updated: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True))

    # 申诉相关字段
    appeal_status: Mapped[Optional[str]] = mapped_column(String, index=True)
    appeal_stage: Mapped[Optional[str]] = mapped_column(String)
    appeal_attempt_count: Mapped[Optional[int]] = mapped_column(Integer, default=0)
    last_appeal_check: Mapped[Optional[datetime]] = mapped_column(DateTime)
    appeal_started_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    appeal_completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime)
    appeal_result: Mapped[Optional[str]] = mapped_column(String)
    appeal_error_message: Mapped[Optional[str]] = mapped_column(Text)
    next_appeal_retry: Mapped[Optional[datetime]] = mapped_column(DateTime)

    account: Mapped["AdAccount"] = relationship(back_populates="campaigns")
    platform_creatives: Mapped[List["PlatformCreative"]] = relationship(secondary=campaign_platform_creative_association, back_populates="campaigns")
    reports: Mapped[List["CampaignReport"]] = relationship(back_populates="campaign")
    performance_snapshots: Mapped[List["CreativePerformanceSnapshot"]] = relationship(back_populates="campaign")


# --- [V-Data-1] 数据仓库模型 ---
class AdvertiserReport(Base):
    """账户级别日/时报表"""
    __tablename__ = 'advertiser_reports'
    id: Mapped[int] = mapped_column(primary_key=True)
    account_id: Mapped[int] = mapped_column(ForeignKey('ad_accounts.id'), index=True)
    stat_datetime: Mapped[datetime] = mapped_column(DateTime(timezone=True), index=True)
    
    stat_cost: Mapped[Optional[float]] = mapped_column(Float, comment="总消耗")
    show_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="展示数")
    click_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="点击数")
    ctr: Mapped[Optional[float]] = mapped_column(Float, comment="点击率")
    cpm_platform: Mapped[Optional[float]] = mapped_column(Float, comment="平均千次展示成本")
    pay_order_count: Mapped[Optional[int]] = mapped_column(Integer, comment="支付订单数")
    pay_order_amount: Mapped[Optional[float]] = mapped_column(Float, comment="支付订单金额")
    prepay_and_pay_order_roi: Mapped[Optional[float]] = mapped_column(Float, comment="支付ROI")
    prepay_order_count: Mapped[Optional[int]] = mapped_column(Integer, comment="预售订单数")
    prepay_order_amount: Mapped[Optional[float]] = mapped_column(Float, comment="预售订单金额")
    live_watch_one_minute_count: Mapped[Optional[int]] = mapped_column(Integer, comment="直播间观看超过1分钟人数")
    live_fans_club_join_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="直播间新加团人数")
    live_comment_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="直播间评论次数")
    live_share_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="直播间分享次数")
    convert_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="转化数")
    conversion_cost: Mapped[Optional[float]] = mapped_column(Float, comment="转化成本")
    conversion_rate: Mapped[Optional[float]] = mapped_column(Float, comment="转化率")

    account: Mapped["AdAccount"] = relationship()

class CampaignReport(Base):
    """计划级别日/时报表"""
    __tablename__ = 'campaign_reports'
    id: Mapped[int] = mapped_column(primary_key=True)
    campaign_id: Mapped[int] = mapped_column(ForeignKey('campaigns.id'), index=True)
    stat_datetime: Mapped[datetime] = mapped_column(DateTime(timezone=True), index=True)

    stat_cost: Mapped[Optional[float]] = mapped_column(Float, comment="总消耗")
    show_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="展示数")
    click_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="点击数")
    ctr: Mapped[Optional[float]] = mapped_column(Float, comment="点击率")
    cpm_platform: Mapped[Optional[float]] = mapped_column(Float, comment="平均千次展示成本")
    pay_order_count: Mapped[Optional[int]] = mapped_column(Integer, comment="支付订单数")
    pay_order_amount: Mapped[Optional[float]] = mapped_column(Float, comment="支付订单金额")
    prepay_and_pay_order_roi: Mapped[Optional[float]] = mapped_column(Float, comment="支付ROI")
    prepay_order_count: Mapped[Optional[int]] = mapped_column(Integer, comment="预售订单数")
    prepay_order_amount: Mapped[Optional[float]] = mapped_column(Float, comment="预售订单金额")
    live_watch_one_minute_count: Mapped[Optional[int]] = mapped_column(Integer, comment="直播间观看超过1分钟人数")
    live_fans_club_join_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="直播间新加团人数")
    live_comment_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="直播间评论次数")
    live_share_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="直播间分享次数")
    convert_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="转化数")
    conversion_cost: Mapped[Optional[float]] = mapped_column(Float, comment="转化成本")
    conversion_rate: Mapped[Optional[float]] = mapped_column(Float, comment="转化率")
    
    campaign: Mapped["Campaign"] = relationship(back_populates="reports")

class MaterialReport(Base):
    """素材级别日/时报表"""
    __tablename__ = 'material_reports'
    id: Mapped[int] = mapped_column(primary_key=True)
    material_id: Mapped[int] = mapped_column(ForeignKey('platform_creatives.id'), index=True)
    stat_datetime: Mapped[datetime] = mapped_column(DateTime(timezone=True), index=True)

    stat_cost: Mapped[Optional[float]] = mapped_column(Float, comment="消耗")
    prepay_and_pay_order_roi: Mapped[Optional[float]] = mapped_column(Float, comment="直接支付ROI")
    pay_order_count: Mapped[Optional[int]] = mapped_column(Integer, comment="直接成交订单数")
    pay_order_amount: Mapped[Optional[float]] = mapped_column(Float, comment="直接成交金额")
    ctr: Mapped[Optional[float]] = mapped_column(Float, comment="点击率")
    conversion_rate: Mapped[Optional[float]] = mapped_column(Float, comment="转化率")
    cpm_platform: Mapped[Optional[float]] = mapped_column(Float, comment="平均千次展现费用")
    conversion_cost: Mapped[Optional[float]] = mapped_column(Float, comment="转化成本")
    convert_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="转化数")
    show_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="展示次数")
    click_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="点击次数")
    live_comment_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="评论次数")
    live_share_cnt: Mapped[Optional[int]] = mapped_column(Integer, comment="分享次数")
    live_watch_one_minute_count: Mapped[Optional[int]] = mapped_column(Integer, comment="直播间超过1分钟观看人次")

    material: Mapped["PlatformCreative"] = relationship()


class CreativePerformanceSnapshot(Base):
    """素材性能快照表 (TimescaleDB Hypertable) - 与数据库真实结构对齐"""
    __tablename__ = 'creative_performance_snapshots'
    
    record_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), primary_key=True)
    creative_id: Mapped[int] = mapped_column(ForeignKey('platform_creatives.id'), primary_key=True)
    
    campaign_id: Mapped[int] = mapped_column(ForeignKey('campaigns.id'), index=True)
    account_id: Mapped[int] = mapped_column(ForeignKey('ad_accounts.id'), index=True)

    platform_creative: Mapped["PlatformCreative"] = relationship(back_populates="performance_snapshots")
    campaign: Mapped["Campaign"] = relationship(back_populates="performance_snapshots")
    account: Mapped["AdAccount"] = relationship(back_populates="performance_snapshots")
    
    review_status: Mapped[str] = mapped_column(String)
    
    total_cost: Mapped[Optional[float]] = mapped_column(Float)
    total_impressions: Mapped[Optional[int]] = mapped_column(Integer)
    total_clicks: Mapped[Optional[int]] = mapped_column(Integer)
    total_conversions: Mapped[Optional[int]] = mapped_column(Integer)
    total_pay_order_amount: Mapped[Optional[float]] = mapped_column(Float)

    __table_args__ = (
        Index('ix_snapshot_campaign_time', 'campaign_id', 'record_time'),
    )
