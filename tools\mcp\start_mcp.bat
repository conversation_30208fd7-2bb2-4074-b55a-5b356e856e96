@echo off
REM Qianchuan Automation Project - MCP Tool Startup Script
REM Activate conda environment and start MCP tool

echo Starting Qianchuan MCP Tool...
echo.

REM Activate conda environment
echo Activating conda environment: qc_env
call C:\ProgramData\anaconda3\Scripts\activate.bat qc_env
if errorlevel 1 (
    echo ERROR: Cannot activate conda environment qc_env
    pause
    exit /b 1
)

REM Set PostgreSQL environment variables
echo Setting database connection parameters...
set PGHOST=localhost
set PGPORT=5432
set PGDATABASE=qianchuan_analytics
set PGUSER=lanfeng
set PGPASSWORD=zmx5062686

REM Set project related environment variables
set QIANCHUAN_PROJECT_ROOT=D:\Project\qianchuangzl
set QIANCHUAN_CONFIG_FILE=D:\Project\qianchuangzl\config\settings.yml
set QIANCHUAN_WORKFLOW_ASSETS_DIR=G:\workflow_assets

REM Set Redis environment variables
set REDIS_HOST=localhost
set REDIS_PORT=6379
set REDIS_DB=0

REM Set API credentials
set QIANCHUAN_API_APP_ID=1785220955494452
set QIANCHUAN_API_SECRET=ec45e041c8f682b2d68684dbfe8f92f1bd95fb47

echo Environment variables set successfully
echo.

REM Start MCP tool
echo Starting MCP tool (PostgreSQL mode)...
D:\Project\qianchuangzl\tools\mcp\toolbox.exe --prebuilt postgres --stdio

REM If tool exits, show error information
if errorlevel 1 (
    echo.
    echo ERROR: MCP tool startup failed
    echo Please check:
    echo 1. PostgreSQL service is running
    echo 2. Database connection parameters are correct
    echo 3. qc_env environment is properly installed
    pause
    exit /b 1
)

echo MCP tool has exited
pause
