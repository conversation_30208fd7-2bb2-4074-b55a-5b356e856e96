#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 维护工具
生命周期: 永久保留
创建目的: 统一系统中的日期格式，解决目录命名不一致问题
清理条件: 问题解决后可归档保留
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.config_loader import load_settings

class DateFormatUnifier:
    """日期格式统一工具"""
    
    def __init__(self):
        self.project_root = project_root
        self.app_settings = load_settings()
        self.base_workflow_dir = "D:/workflow_assets"
        
        # 定义标准日期格式 (无连字符格式)
        self.standard_date_format = '%Y%m%d'
        
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'directories_analyzed': [],
            'directories_migrated': [],
            'files_moved': 0,
            'code_files_updated': [],
            'errors': []
        }

    def analyze_date_format_inconsistencies(self):
        """分析日期格式不一致问题"""
        logger.info("🔍 分析日期格式不一致问题...")
        
        # 检查审核通过目录
        approved_dir = os.path.join(self.base_workflow_dir, "03_materials_approved", "缇萃百货")
        
        if not os.path.exists(approved_dir):
            logger.warning(f"目录不存在: {approved_dir}")
            return
        
        date_directories = []
        for item in os.listdir(approved_dir):
            item_path = os.path.join(approved_dir, item)
            if os.path.isdir(item_path):
                date_directories.append(item)
        
        # 分类日期格式
        with_dash_format = []  # 2025-08-05 格式
        without_dash_format = []  # 20250805 格式
        
        for dir_name in date_directories:
            if '-' in dir_name and len(dir_name) == 10:  # YYYY-MM-DD
                with_dash_format.append(dir_name)
            elif '-' not in dir_name and len(dir_name) == 8:  # YYYYMMDD
                without_dash_format.append(dir_name)
        
        logger.info(f"📊 发现日期目录统计:")
        logger.info(f"  - 带连字符格式 (YYYY-MM-DD): {len(with_dash_format)} 个")
        logger.info(f"  - 无连字符格式 (YYYYMMDD): {len(without_dash_format)} 个")
        
        if with_dash_format:
            logger.info(f"  - 带连字符目录: {with_dash_format}")
        if without_dash_format:
            logger.info(f"  - 无连字符目录: {without_dash_format}")
        
        self.results['directories_analyzed'] = {
            'with_dash': with_dash_format,
            'without_dash': without_dash_format,
            'total': len(date_directories)
        }
        
        return with_dash_format, without_dash_format

    def migrate_directories_to_standard_format(self, with_dash_dirs: List[str], dry_run: bool = True):
        """迁移目录到标准格式"""
        logger.info(f"🔄 {'预览' if dry_run else '执行'}目录格式统一...")
        
        approved_dir = os.path.join(self.base_workflow_dir, "03_materials_approved", "缇萃百货")
        migration_plan = []
        
        for dash_dir in with_dash_dirs:
            # 转换格式: 2025-08-05 -> 20250805
            try:
                date_obj = datetime.strptime(dash_dir, '%Y-%m-%d')
                standard_dir = date_obj.strftime(self.standard_date_format)
                
                source_path = os.path.join(approved_dir, dash_dir)
                target_path = os.path.join(approved_dir, standard_dir)
                
                # 检查源目录中的文件
                files_in_source = []
                if os.path.exists(source_path):
                    files_in_source = [f for f in os.listdir(source_path) if os.path.isfile(os.path.join(source_path, f))]
                
                migration_plan.append({
                    'source': dash_dir,
                    'target': standard_dir,
                    'source_path': source_path,
                    'target_path': target_path,
                    'files_count': len(files_in_source),
                    'files': files_in_source
                })
                
                logger.info(f"  📁 {dash_dir} -> {standard_dir} ({len(files_in_source)} 个文件)")
                
            except ValueError as e:
                logger.error(f"❌ 无法解析日期格式: {dash_dir} - {e}")
                self.results['errors'].append(f"日期解析失败: {dash_dir}")
        
        if not dry_run:
            return self._execute_migration(migration_plan)
        else:
            logger.info(f"📋 预览完成，计划迁移 {len(migration_plan)} 个目录")
            return migration_plan

    def _execute_migration(self, migration_plan: List[Dict]):
        """执行迁移操作"""
        logger.info("🚀 开始执行目录迁移...")
        
        migrated_dirs = []
        total_files_moved = 0
        
        for plan in migration_plan:
            try:
                source_path = plan['source_path']
                target_path = plan['target_path']
                
                if not os.path.exists(source_path):
                    logger.warning(f"⚠️ 源目录不存在: {source_path}")
                    continue
                
                # 创建目标目录
                os.makedirs(target_path, exist_ok=True)
                
                # 移动文件
                files_moved = 0
                for filename in plan['files']:
                    source_file = os.path.join(source_path, filename)
                    target_file = os.path.join(target_path, filename)
                    
                    if os.path.exists(target_file):
                        logger.warning(f"⚠️ 目标文件已存在，跳过: {filename}")
                        continue
                    
                    shutil.move(source_file, target_file)
                    files_moved += 1
                
                # 删除空的源目录
                if not os.listdir(source_path):
                    os.rmdir(source_path)
                    logger.success(f"✅ 已删除空目录: {plan['source']}")
                
                migrated_dirs.append(plan['source'])
                total_files_moved += files_moved
                
                logger.success(f"✅ 迁移完成: {plan['source']} -> {plan['target']} ({files_moved} 个文件)")
                
            except Exception as e:
                logger.error(f"❌ 迁移失败: {plan['source']} - {e}")
                self.results['errors'].append(f"迁移失败: {plan['source']} - {str(e)}")
        
        self.results['directories_migrated'] = migrated_dirs
        self.results['files_moved'] = total_files_moved
        
        logger.success(f"🎉 迁移完成: {len(migrated_dirs)} 个目录, {total_files_moved} 个文件")
        return migrated_dirs

    def update_code_date_formats(self):
        """更新代码中的日期格式"""
        logger.info("🔧 更新代码中的日期格式...")
        
        # 需要更新的文件列表
        files_to_update = [
            {
                'path': 'src/qianchuan_aw/workflows/independent_material_harvest.py',
                'changes': [
                    {
                        'line_pattern': "date_str = datetime.now().strftime('%Y-%m-%d')",
                        'replacement': "date_str = datetime.now().strftime('%Y%m%d')",
                        'description': "统一收割工作流日期格式"
                    }
                ]
            }
        ]
        
        updated_files = []
        
        for file_info in files_to_update:
            file_path = self.project_root / file_info['path']
            
            if not file_path.exists():
                logger.warning(f"⚠️ 文件不存在: {file_path}")
                continue
            
            try:
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 应用更改
                updated = False
                for change in file_info['changes']:
                    if change['line_pattern'] in content:
                        content = content.replace(change['line_pattern'], change['replacement'])
                        updated = True
                        logger.info(f"  ✅ {change['description']}")
                
                if updated:
                    # 写回文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    updated_files.append(str(file_path))
                    logger.success(f"✅ 更新文件: {file_info['path']}")
                else:
                    logger.info(f"ℹ️ 文件无需更新: {file_info['path']}")
                    
            except Exception as e:
                logger.error(f"❌ 更新文件失败: {file_info['path']} - {e}")
                self.results['errors'].append(f"文件更新失败: {file_info['path']} - {str(e)}")
        
        self.results['code_files_updated'] = updated_files
        return updated_files

    def generate_report(self):
        """生成统一报告"""
        report_path = self.project_root / 'ai_reports' / 'maintenance' / f'ai_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}_date_format_unification.json'
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 统一报告已保存: {report_path}")

    def run_full_unification(self, dry_run: bool = True):
        """运行完整的日期格式统一"""
        logger.info("🚀 开始日期格式统一...")
        
        try:
            # 1. 分析不一致问题
            with_dash, without_dash = self.analyze_date_format_inconsistencies()
            
            if not with_dash:
                logger.success("✅ 没有发现需要统一的日期格式目录")
                return self.results
            
            # 2. 迁移目录
            if with_dash:
                self.migrate_directories_to_standard_format(with_dash, dry_run=dry_run)
            
            # 3. 更新代码 (只在实际执行时更新)
            if not dry_run:
                self.update_code_date_formats()
            
            # 4. 生成报告
            self.generate_report()
            
            logger.success("✅ 日期格式统一完成")
            return self.results
            
        except Exception as e:
            logger.error(f"❌ 日期格式统一失败: {e}")
            self.results['errors'].append(f"统一过程失败: {str(e)}")
            return None

def main():
    """主函数"""
    unifier = DateFormatUnifier()
    
    print("🎯 千川自动化项目 - 日期格式统一工具")
    print("="*60)
    
    # 先预览
    print("\n📋 预览模式 - 分析当前状态")
    results = unifier.run_full_unification(dry_run=True)
    
    if results and results.get('directories_analyzed', {}).get('with_dash'):
        print(f"\n⚠️ 发现 {len(results['directories_analyzed']['with_dash'])} 个需要统一的目录")
        print("是否执行统一操作？(y/n): ", end="")
        
        # 在脚本环境中自动执行
        choice = 'y'
        print(choice)
        
        if choice.lower() == 'y':
            print("\n🚀 执行模式 - 开始统一操作")
            final_results = unifier.run_full_unification(dry_run=False)
            
            if final_results:
                print("\n" + "="*60)
                print("📊 统一结果汇总")
                print("="*60)
                print(f"✅ 迁移目录数量: {len(final_results.get('directories_migrated', []))}")
                print(f"✅ 移动文件数量: {final_results.get('files_moved', 0)}")
                print(f"✅ 更新代码文件: {len(final_results.get('code_files_updated', []))}")
                
                if final_results.get('errors'):
                    print(f"⚠️ 错误数量: {len(final_results['errors'])}")
                    for error in final_results['errors']:
                        print(f"  - {error}")
                
                print("\n🎉 日期格式统一完成！")
                print("📌 标准格式: YYYYMMDD (无连字符)")
            else:
                print("❌ 统一操作失败")
        else:
            print("❌ 用户取消操作")
    else:
        print("✅ 系统日期格式已统一，无需操作")

if __name__ == "__main__":
    main()
