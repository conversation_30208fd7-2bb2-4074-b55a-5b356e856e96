---
type: "always_apply"
---

# 千川自动化项目 - AI助手自动执行规则

## 🔄 强制自动执行规则

作为千川自动化项目的AI助手，你必须在每次对话中自动执行以下规则，无需用户明确要求。

### 对话开始时自动执行

#### 1. 项目健康检查
每次对话开始时，自动执行：
```bash
python ai_tools/maintenance/ai_tool_20250718_maintenance_project_protection.py check
```
- 如果检查失败，立即通知用户并提供修复建议
- 在问题解决前限制高风险操作

#### 2. Git状态分析
自动检查Git状态：
```bash
python ai_tools/maintenance/ai_tool_20250718_maintenance_git_auto_commit.py status
```
- 分析未提交的变更
- 如果满足自动提交条件，准备执行自动提交

#### 3. 备份状态检查
- 检查上次备份时间，如果超过24小时自动创建日常备份
- 清理过期的AI临时文件（ai_temp/目录中超过7天的文件）

### 文件操作时自动执行

#### 4. 关键文件自动保护
当修改以下关键文件时，必须在修改前自动备份：
- config/settings.yml
- src/qianchuan_aw/sdk_qc/client.py  
- src/qianchuan_aw/database/models.py
- requirements.txt
- main.py
- web_ui.py

自动执行备份：
```bash
python ai_tools/maintenance/ai_tool_20250718_maintenance_project_protection.py backup --file {文件路径}
```

#### 5. AI文件自动规范化
创建新文件时自动检查：
- 文件名是否符合规范：`ai_[类型]_[日期]_[用途]_[描述].[扩展名]`
- 是否放置在正确目录：ai_tools/, ai_temp/, ai_reports/, ai_templates/
- 是否需要添加文件头部生命周期信息

#### 6. 敏感内容自动检测
- 扫描文件中的密码、token、密钥等敏感信息
- 如果检测到敏感内容，警告用户并建议添加到.gitignore
- 阻止敏感文件被提交到Git

### Git操作时自动执行

#### 7. 智能自动提交
当满足以下任一条件时，自动执行提交：
- 修改文件数量 ≥ 5个
- 关键文件发生变更
- API相关文件变更
- AI工具文件更新

自动执行流程：
```bash
# 1. 运行安全检查
# 2. 如果通过，执行自动提交
python ai_tools/maintenance/ai_tool_20250718_maintenance_git_auto_commit.py commit
```

#### 8. 提交前安全检查
每次Git提交前自动执行：
- Python语法检查
- YAML配置文件格式验证
- 敏感文件泄露检测
- 如果检查失败，阻止提交并提供修复建议

### 数据库操作时自动执行

#### 9. 数据库文件变更自动备份
当修改src/qianchuan_aw/database/目录下任何文件时，自动备份数据库：
```bash
python ai_tools/maintenance/ai_tool_20250718_maintenance_project_protection.py backup --type database
```

### 对话结束时自动执行

#### 10. AI文件价值评估
- 扫描本次对话创建的所有文件
- 评估文件价值（临时/重要/永久）
- 提供文件管理建议

#### 11. 自动清理和维护
```bash
# 清理过期的AI临时文件
python tools/ai_file_manager.py cleanup --type temp

# 执行维护任务
python ai_tools/maintenance/ai_tool_20250718_maintenance_project_protection.py maintenance
```

#### 12. 管理工具使用提醒
提醒用户使用以下命令查看状态：
```bash
python tools/ai_file_manager.py status
python ai_tools/maintenance/ai_tool_20250718_maintenance_git_auto_commit.py status
```

## 🚨 错误处理规则

### 自动错误处理
- 记录所有自动化操作的错误日志
- 尝试自动修复常见问题
- 如果无法自动修复，通知用户并提供解决方案
- 关键操作失败时自动创建紧急备份

### 回滚机制
- Git提交失败时自动回滚到上一状态
- 文件修改失败时从备份恢复
- 配置变更失败时恢复配置文件

## 📋 执行检查清单

每次对话中必须确认以下检查清单：

**对话开始时**：
- [ ] 项目状态检查
- [ ] Git状态分析
- [ ] 备份时间检查
- [ ] AI临时文件清理检查

**文件操作时**：
- [ ] 关键文件备份
- [ ] AI文件规范化
- [ ] 敏感文件检测

**Git操作时**：
- [ ] 安全检查
- [ ] 自动提交条件判断
- [ ] 提交消息标准化

**对话结束时**：
- [ ] AI文件价值评估
- [ ] 自动清理执行
- [ ] 管理工具提醒

## 🎯 通知格式

每次自动执行后，使用以下格式通知用户：

```
🤖 自动化操作执行：
✅ 项目状态检查通过
✅ 检测到config/settings.yml变更，已自动备份
⚠️ 发现5个文件变更，满足自动提交条件
🔄 正在执行自动提交...
✅ 自动提交成功 (commit: abc123)
💡 建议：查看提交详情 'git log -1'
```

## ⚡ 优先级顺序

1. **最高优先级**：安全检查和关键文件保护
2. **高优先级**：Git管理和文件规范化
3. **中优先级**：备份和清理操作
4. **低优先级**：通知和提醒操作

## 🔧 工具集成路径

所有自动化操作通过以下工具执行：
- **Git管理**：`ai_tools/maintenance/ai_tool_20250718_maintenance_git_auto_commit.py`
- **项目保护**：`ai_tools/maintenance/ai_tool_20250718_maintenance_project_protection.py`
- **AI文件管理**：`tools/ai_file_manager.py`
- **规则引擎**：`ai_tools/maintenance/ai_tool_20250718_maintenance_auto_rules_engine.py`

## 📊 自动化效果监控

### 成功指标
- 自动化操作成功率 > 95%
- 关键文件保护覆盖率 = 100%
- Git提交规范化率 = 100%
- AI文件管理规范化率 > 90%

### 失败处理
- 自动化操作失败时立即通知用户
- 提供详细的错误信息和解决方案
- 记录失败日志用于后续优化

---

**重要提醒**：这些规则是强制性的，必须在每次对话中严格执行。如果任何自动化操作失败，必须立即通知用户并提供解决方案。这确保了千川自动化项目的安全性、规范性和高度自动化。
