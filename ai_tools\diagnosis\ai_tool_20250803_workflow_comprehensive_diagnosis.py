#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 综合诊断工作流系统问题，特别是入库文件目录处理问题
清理条件: 成为项目核心诊断工具，长期保留
"""

import os
import sys
import json
from datetime import datetime, timedelta
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO"
    )

def check_directory_files():
    """检查入库目录中的文件"""
    logger.info("🔍 检查入库目录文件状态...")
    
    directory = "D:/workflow_assets/01_materials_to_process/缇萃百货"
    
    if not os.path.exists(directory):
        logger.error(f"❌ 入库目录不存在: {directory}")
        return []
    
    files = []
    for filename in os.listdir(directory):
        if filename.endswith('.mp4'):
            file_path = os.path.join(directory, filename)
            file_stat = os.stat(file_path)
            files.append({
                'filename': filename,
                'path': file_path,
                'size': file_stat.st_size,
                'modified': datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            })
    
    logger.info(f"📊 发现 {len(files)} 个视频文件:")
    for file_info in files:
        logger.info(f"   📄 {file_info['filename']} ({file_info['size']/1024/1024:.1f}MB, {file_info['modified']})")
    
    return files

def check_database_records():
    """检查数据库中的记录"""
    logger.info("🔍 检查数据库记录状态...")
    
    try:
        from qianchuan_aw.database.connection import database_session
        from qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            # 检查8.1和8.3文件的记录
            records = db.query(LocalCreative).filter(
                LocalCreative.filename.like('8.1-%') | 
                LocalCreative.filename.like('8.3-%')
            ).order_by(LocalCreative.created_at.desc()).limit(50).all()
            
            logger.info(f"📊 数据库中相关记录: {len(records)} 条")
            
            # 按状态分组统计
            status_counts = {}
            path_issues = []
            
            for record in records:
                status = record.status
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # 检查路径问题
                if record.file_path and '01_to_process' in record.file_path:
                    path_issues.append({
                        'filename': record.filename,
                        'old_path': record.file_path,
                        'status': record.status,
                        'created_at': record.created_at
                    })
            
            logger.info("📈 状态统计:")
            for status, count in status_counts.items():
                logger.info(f"   {status}: {count} 条")
            
            if path_issues:
                logger.warning(f"⚠️ 发现 {len(path_issues)} 条路径问题记录:")
                for issue in path_issues:
                    logger.warning(f"   {issue['filename']} - {issue['status']} - {issue['old_path']}")
            
            return records, path_issues
            
    except Exception as e:
        logger.error(f"❌ 数据库查询失败: {e}")
        return [], []

def check_celery_tasks():
    """检查Celery任务状态"""
    logger.info("🔍 检查Celery任务状态...")
    
    try:
        # 检查最近的日志
        log_file = "logs/app_2025-08-03.log"
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 查找最近的任务执行记录
            recent_tasks = []
            for line in reversed(lines[-1000:]):  # 检查最后1000行
                if 'Task Start' in line or 'Task End' in line:
                    recent_tasks.append(line.strip())
                    if len(recent_tasks) >= 10:
                        break
            
            logger.info("📋 最近的任务执行记录:")
            for task in reversed(recent_tasks):
                logger.info(f"   {task}")
        
        # 检查Celery Beat调度文件
        beat_file = "logs/celerybeat-schedule.db"
        if os.path.exists(beat_file):
            stat = os.stat(beat_file)
            last_modified = datetime.fromtimestamp(stat.st_mtime)
            logger.info(f"📅 Celery Beat调度文件最后更新: {last_modified}")
        else:
            logger.warning("⚠️ Celery Beat调度文件不存在")
            
    except Exception as e:
        logger.error(f"❌ 检查Celery任务失败: {e}")

def analyze_workflow_bottlenecks():
    """分析工作流瓶颈"""
    logger.info("🔍 分析工作流瓶颈...")
    
    try:
        from qianchuan_aw.database.connection import database_session
        from qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            # 检查各状态的素材数量
            status_query = db.query(
                LocalCreative.status,
                db.func.count(LocalCreative.id).label('count')
            ).group_by(LocalCreative.status).all()
            
            logger.info("📊 各状态素材统计:")
            bottlenecks = []
            
            for status, count in status_query:
                logger.info(f"   {status}: {count} 个")
                
                # 识别可能的瓶颈
                if status in ['pending_grouping', 'pending_upload'] and count > 0:
                    bottlenecks.append({
                        'status': status,
                        'count': count,
                        'description': f"{count}个素材卡在{status}状态"
                    })
            
            if bottlenecks:
                logger.warning("⚠️ 发现工作流瓶颈:")
                for bottleneck in bottlenecks:
                    logger.warning(f"   {bottleneck['description']}")
            
            return bottlenecks
            
    except Exception as e:
        logger.error(f"❌ 分析工作流瓶颈失败: {e}")
        return []

def fix_path_issues(path_issues):
    """修复路径问题"""
    if not path_issues:
        logger.info("✅ 没有发现路径问题")
        return True
    
    logger.info(f"🔧 开始修复 {len(path_issues)} 个路径问题...")
    
    try:
        from qianchuan_aw.database.connection import database_session
        from qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            fixed_count = 0
            
            for issue in path_issues:
                # 查找记录
                record = db.query(LocalCreative).filter(
                    LocalCreative.filename == issue['filename']
                ).first()
                
                if record:
                    # 构建新路径
                    old_path = record.file_path
                    new_path = old_path.replace('01_to_process', '01_materials_to_process')
                    
                    # 检查新路径是否存在
                    if os.path.exists(new_path):
                        record.file_path = new_path
                        record.updated_at = datetime.now()
                        fixed_count += 1
                        logger.success(f"✅ 修复路径: {issue['filename']}")
                    else:
                        logger.warning(f"⚠️ 新路径不存在: {new_path}")
            
            db.commit()
            logger.success(f"🎯 成功修复 {fixed_count} 个路径问题")
            return True
            
    except Exception as e:
        logger.error(f"❌ 修复路径问题失败: {e}")
        return False

def trigger_workflow_tasks():
    """触发工作流任务"""
    logger.info("🚀 触发工作流任务...")
    
    try:
        from qianchuan_aw.workflows.tasks import (
            task_ingest_and_upload,
            task_group_and_dispatch
        )
        
        # 触发文件摄取任务
        task_ingest_and_upload.delay()
        logger.success("✅ 已触发文件摄取任务")
        
        # 触发分组派发任务
        task_group_and_dispatch.delay()
        logger.success("✅ 已触发分组派发任务")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 触发工作流任务失败: {e}")
        return False

def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 80)
    logger.info("🔧 工作流系统综合诊断工具")
    logger.info("=" * 80)
    
    # 1. 检查目录文件
    directory_files = check_directory_files()
    
    # 2. 检查数据库记录
    db_records, path_issues = check_database_records()
    
    # 3. 检查Celery任务
    check_celery_tasks()
    
    # 4. 分析工作流瓶颈
    bottlenecks = analyze_workflow_bottlenecks()
    
    # 5. 修复路径问题
    if path_issues:
        fix_path_issues(path_issues)
    
    # 6. 触发工作流任务
    trigger_workflow_tasks()
    
    # 生成诊断报告
    report = {
        'timestamp': datetime.now().isoformat(),
        'directory_files_count': len(directory_files),
        'database_records_count': len(db_records),
        'path_issues_count': len(path_issues),
        'bottlenecks_count': len(bottlenecks),
        'directory_files': directory_files,
        'path_issues': path_issues,
        'bottlenecks': bottlenecks
    }
    
    # 保存报告
    report_file = f"ai_reports/diagnosis/ai_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_workflow_diagnosis.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logger.success(f"📋 诊断报告已保存: {report_file}")
    
    logger.info("=" * 80)
    logger.info("🎯 诊断总结:")
    logger.info(f"   📁 入库目录文件: {len(directory_files)} 个")
    logger.info(f"   💾 数据库记录: {len(db_records)} 条")
    logger.info(f"   🔧 路径问题: {len(path_issues)} 个")
    logger.info(f"   ⚠️ 工作流瓶颈: {len(bottlenecks)} 个")
    logger.info("=" * 80)

if __name__ == "__main__":
    main()
