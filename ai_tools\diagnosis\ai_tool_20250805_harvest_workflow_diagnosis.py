#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 诊断工具
生命周期: 临时工具
创建目的: 诊断收割工作流为什么没有执行
清理条件: 问题解决后可删除
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.utils.config_loader import load_settings
from qianchuan_aw.database.models import Campaign, LocalCreative, PlatformCreative, AdAccount
from qianchuan_aw.workflows.scheduler import handle_independent_material_harvest, handle_monitoring_of_materials
from qianchuan_aw.utils.logger import logger
from sqlalchemy.orm import joinedload
from sqlalchemy import func

class HarvestWorkflowDiagnostic:
    def __init__(self):
        self.app_settings = load_settings()
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'config_check': {},
            'data_check': {},
            'workflow_test': {},
            'recommendations': []
        }

    def check_harvest_configuration(self):
        """检查收割工作流配置"""
        logger.info("🔍 检查收割工作流配置...")
        
        workflow_config = self.app_settings.get('workflow', {})
        
        # 检查独立收割配置
        independent_harvest = workflow_config.get('independent_harvest', {})
        material_monitoring = workflow_config.get('material_monitoring', {})
        
        self.results['config_check'] = {
            'independent_harvest_enabled': independent_harvest.get('enabled', False),
            'independent_harvest_interval': independent_harvest.get('interval_seconds', 0),
            'material_monitoring_enabled': material_monitoring.get('enabled', False),
            'material_monitoring_interval': material_monitoring.get('interval_seconds', 0),
            'test_accounts_only': independent_harvest.get('scope', {}).get('test_accounts_only', True),
            'account_type_filter': independent_harvest.get('scope', {}).get('account_type_filter', [])
        }
        
        logger.info(f"📊 独立收割启用: {self.results['config_check']['independent_harvest_enabled']}")
        logger.info(f"📊 素材监控启用: {self.results['config_check']['material_monitoring_enabled']}")
        
        return self.results['config_check']

    def check_harvest_data_status(self):
        """检查收割相关数据状态"""
        logger.info("🔍 检查收割相关数据状态...")
        
        with database_session() as db:
            # 检查素材收割状态
            harvest_status_stats = db.query(
                LocalCreative.harvest_status,
                func.count(LocalCreative.id).label('count')
            ).group_by(LocalCreative.harvest_status).all()
            
            # 检查最近24小时的计划状态
            recent_campaigns = db.query(
                Campaign.status,
                func.count(Campaign.id).label('count')
            ).join(AdAccount).filter(
                AdAccount.account_type == 'TEST',
                Campaign.created_at >= datetime.utcnow() - timedelta(hours=24)
            ).group_by(Campaign.status).all()
            
            # 检查可收割的素材
            harvestable_materials = db.query(LocalCreative).join(
                PlatformCreative
            ).join(
                PlatformCreative.campaigns
            ).join(AdAccount).filter(
                AdAccount.account_type == 'TEST',
                LocalCreative.status == 'approved',
                LocalCreative.harvest_status == 'not_harvested'
            ).count()
            
            self.results['data_check'] = {
                'harvest_status_distribution': {status: count for status, count in harvest_status_stats},
                'recent_campaign_status': {status: count for status, count in recent_campaigns},
                'harvestable_materials_count': harvestable_materials,
                'total_approved_materials': db.query(LocalCreative).filter(
                    LocalCreative.status == 'approved'
                ).count()
            }
            
            logger.info(f"📊 可收割素材数量: {harvestable_materials}")
            logger.info(f"📊 总审核通过素材: {self.results['data_check']['total_approved_materials']}")
            
        return self.results['data_check']

    def test_harvest_workflow_execution(self):
        """测试收割工作流执行"""
        logger.info("🔍 测试收割工作流执行...")
        
        test_results = {
            'independent_harvest_test': {'success': False, 'error': None},
            'material_monitoring_test': {'success': False, 'error': None}
        }
        
        with database_session() as db:
            # 测试独立素材收割
            try:
                logger.info("🧪 测试独立素材收割...")
                handle_independent_material_harvest(db, self.app_settings)
                test_results['independent_harvest_test']['success'] = True
                logger.success("✅ 独立素材收割测试成功")
            except Exception as e:
                test_results['independent_harvest_test']['error'] = str(e)
                logger.error(f"❌ 独立素材收割测试失败: {e}")
            
            # 测试素材监控收割
            try:
                logger.info("🧪 测试素材监控收割...")
                handle_monitoring_of_materials(db, self.app_settings)
                test_results['material_monitoring_test']['success'] = True
                logger.success("✅ 素材监控收割测试成功")
            except Exception as e:
                test_results['material_monitoring_test']['error'] = str(e)
                logger.error(f"❌ 素材监控收割测试失败: {e}")
        
        self.results['workflow_test'] = test_results
        return test_results

    def generate_recommendations(self):
        """生成修复建议"""
        logger.info("🔍 生成修复建议...")
        
        recommendations = []
        
        # 检查配置问题
        if not self.results['config_check']['independent_harvest_enabled']:
            recommendations.append("启用独立收割工作流: workflow.independent_harvest.enabled = true")
        
        if not self.results['config_check']['material_monitoring_enabled']:
            recommendations.append("启用素材监控工作流: workflow.material_monitoring.enabled = true")
        
        # 检查数据问题
        if self.results['data_check']['harvestable_materials_count'] > 0:
            recommendations.append(f"发现 {self.results['data_check']['harvestable_materials_count']} 个可收割素材，需要立即执行收割")
        
        # 检查工作流执行问题
        if not self.results['workflow_test']['independent_harvest_test']['success']:
            error = self.results['workflow_test']['independent_harvest_test']['error']
            recommendations.append(f"修复独立收割工作流错误: {error}")
        
        if not self.results['workflow_test']['material_monitoring_test']['success']:
            error = self.results['workflow_test']['material_monitoring_test']['error']
            recommendations.append(f"修复素材监控工作流错误: {error}")
        
        # 检查Celery调度问题
        recommendations.append("检查Celery Beat调度器是否正常运行")
        recommendations.append("检查Redis连接是否正常")
        
        self.results['recommendations'] = recommendations
        return recommendations

    def run_full_diagnosis(self):
        """运行完整诊断"""
        logger.info("🚀 开始收割工作流完整诊断...")
        
        try:
            self.check_harvest_configuration()
            self.check_harvest_data_status()
            self.test_harvest_workflow_execution()
            self.generate_recommendations()
            
            # 生成诊断报告
            self.generate_report()
            
            logger.success("✅ 收割工作流诊断完成")
            return self.results
            
        except Exception as e:
            logger.error(f"❌ 诊断过程发生错误: {e}")
            return None

    def generate_report(self):
        """生成诊断报告"""
        report_path = project_root / 'ai_reports' / 'diagnosis' / f'ai_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}_harvest_workflow_diagnosis.json'
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📄 诊断报告已保存: {report_path}")

def main():
    """主函数"""
    diagnostic = HarvestWorkflowDiagnostic()
    results = diagnostic.run_full_diagnosis()
    
    if results:
        print("\n" + "="*60)
        print("🌾 收割工作流诊断结果")
        print("="*60)
        
        print(f"\n📊 配置检查:")
        for key, value in results['config_check'].items():
            print(f"  - {key}: {value}")
        
        print(f"\n📊 数据状态:")
        for key, value in results['data_check'].items():
            print(f"  - {key}: {value}")
        
        print(f"\n🧪 工作流测试:")
        for key, value in results['workflow_test'].items():
            status = "✅ 成功" if value['success'] else f"❌ 失败: {value['error']}"
            print(f"  - {key}: {status}")
        
        print(f"\n💡 修复建议:")
        for i, rec in enumerate(results['recommendations'], 1):
            print(f"  {i}. {rec}")
        
        print("\n" + "="*60)

if __name__ == "__main__":
    main()
