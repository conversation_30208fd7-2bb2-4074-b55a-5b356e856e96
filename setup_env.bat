@echo off
REM 千川自动化项目环境配置脚本
REM 用途: 激活conda环境并运行Python脚本

echo 🚀 激活千川自动化环境...

REM 激活conda环境
call conda activate qc_env

REM 检查环境是否激活成功
python --version
if %errorlevel% neq 0 (
    echo ❌ Python环境激活失败
    pause
    exit /b 1
)

echo ✅ 环境激活成功！
echo 💡 现在可以运行Python脚本了

REM 如果有参数，直接运行
if "%1"=="" (
    echo 用法: setup_env.bat [python脚本路径]
    echo 示例: setup_env.bat ai_temp/test_script.py
) else (
    echo 🔄 运行脚本: %*
    python %*
)

pause
