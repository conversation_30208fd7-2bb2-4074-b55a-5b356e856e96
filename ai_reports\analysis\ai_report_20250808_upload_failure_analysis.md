# Web上传大量失败问题分析报告

**时间**: 2025-08-08 06:49  
**问题**: 大量上传失败，数据库连接池耗尽  
**错误**: `QueuePool limit of size 5 overflow 10 reached, connection timed out`  

---

## 🎯 问题根本原因

### 1. **数据库连接池配置不一致** ❌
**配置文件中的设置**:
```yaml
database:
  connection_pool:
    pool_size: 20        # 配置文件中设置20个连接
    max_overflow: 30     # 最大溢出30个
    pool_timeout: 20     # 超时20秒
```

**实际生效的设置**: 
- 错误信息显示 `QueuePool limit of size 5 overflow 10`
- 说明实际只有5个基础连接 + 10个溢出连接 = 15个总连接

**问题**: `database.py` 中没有应用配置文件中的连接池参数！

### 2. **高并发上传设计缺陷** ❌
**当前配置**:
```yaml
workflow:
  max_upload_workers: 15    # 15个并发上传线程
```

**问题分析**:
- 15个并发上传线程
- 每个线程在 `upload_and_register_task` 中会创建多个数据库连接：
  - 文件检查时: `with database_session() as db:`
  - 上传验证时: 多次API调用需要数据库查询
  - 数据库记录创建时: `with database_session() as db:`
- **每个上传任务可能同时占用2-3个数据库连接**
- **15个并发 × 3个连接 = 45个连接需求**
- **但实际只有15个连接可用** → 连接池耗尽

### 3. **数据库连接未正确释放** ⚠️
在 `upload_and_register_task` 函数中：
```python
# 问题代码模式
with database_session() as db:  # 连接1
    # ... 文件处理
    for attempt in range(1, max_retries + 1):
        # ... 上传逻辑
        library_result = client.get_library_videos(...)  # 可能触发新连接
        # ... 验证逻辑
        # 长时间持有连接，直到整个上传完成
```

**问题**: 每个上传任务持有数据库连接时间过长（包含网络上传时间）

---

## 📊 失败模式分析

### 典型失败场景
1. **启动15个并发上传任务**
2. **每个任务获取数据库连接进行文件检查**
3. **连接池快速耗尽** (15个连接 < 15个任务)
4. **后续任务等待连接超时** (30秒)
5. **大量任务失败，错误: `connection timed out`**

### 错误传播
- 上传失败 → 重试机制 → 更多连接需求 → 连接池压力增大
- 失败任务积压 → 系统性能下降 → 更多超时

---

## ✅ 解决方案

### 1. **修复数据库连接池配置** (立即执行)

修改 `src/qianchuan_aw/database/database.py`:
```python
# 当前代码 (有问题)
engine = create_engine(DATABASE_URL, **engine_args)

# 修复后代码
def create_optimized_engine(database_url: str, settings: dict):
    """创建优化的数据库引擎"""
    pool_config = settings.get('database', {}).get('connection_pool', {})
    
    engine_args = {
        'pool_size': pool_config.get('pool_size', 20),
        'max_overflow': pool_config.get('max_overflow', 30), 
        'pool_timeout': pool_config.get('pool_timeout', 20),
        'pool_recycle': pool_config.get('pool_recycle', 1800),
        'pool_pre_ping': pool_config.get('pool_pre_ping', True),
        'echo': pool_config.get('echo', False)
    }
    
    if settings.get('database', {}).get('type') == 'sqlite':
        engine_args['connect_args'] = {"check_same_thread": False}
    
    return create_engine(database_url, **engine_args)

engine = create_optimized_engine(DATABASE_URL, settings)
```

### 2. **优化上传任务数据库使用** (立即执行)

修改 `tools/manual_launch.py` 中的 `upload_and_register_task`:
```python
def upload_and_register_task(client, principal_id, account_id, video_path):
    """优化版本 - 减少数据库连接持有时间"""
    
    # 1. 快速数据库检查 (短连接)
    with database_session() as db:
        file_hash = get_file_md5(video_path)
        local_creative = find_or_create_local_creative(db, principal_id, file_hash, video_path)
        account = db.get(AdAccount, account_id)
        
        existing_pc = db.query(PlatformCreative).filter_by(
            local_creative_id=local_creative.id, 
            account_id=account.id
        ).first()
        
        if existing_pc:
            return existing_pc.id
    
    # 2. 执行上传 (无数据库连接)
    video_info = client.upload_video(...)
    
    # 3. 快速数据库写入 (短连接)
    with database_session() as db:
        platform_creative = PlatformCreative(...)
        db.add(platform_creative)
        db.commit()
        return platform_creative.id
```

### 3. **降低并发度** (立即执行)

修改 `config/settings.yml`:
```yaml
workflow:
  max_upload_workers: 5  # 从15降到5，确保连接池够用
```

### 4. **添加连接池监控** (建议执行)

创建连接池监控工具：
```python
def monitor_connection_pool():
    """监控数据库连接池状态"""
    pool = engine.pool
    return {
        'pool_size': pool.size(),
        'checked_in': pool.checkedin(),
        'checked_out': pool.checkedout(),
        'overflow': pool.overflow(),
        'invalid': pool.invalid()
    }
```

---

## 🚨 紧急修复步骤

### 步骤1: 立即降低并发度
```yaml
# 修改 config/settings.yml
workflow:
  max_upload_workers: 3  # 紧急降低到3
```

### 步骤2: 重启服务
```bash
# 重启Celery服务以应用新配置
conda activate qc_env
# 停止现有服务，重新启动
```

### 步骤3: 修复数据库引擎配置
- 修改 `database.py` 应用连接池配置
- 确保连接池参数生效

---

## 📈 预期改善效果

### 修复前
- 并发上传: 15个
- 数据库连接: 5+10=15个
- 连接需求: 15×3=45个
- **结果**: 连接池耗尽 ❌

### 修复后
- 并发上传: 5个
- 数据库连接: 20+30=50个
- 连接需求: 5×2=10个
- **结果**: 连接充足 ✅

---

## 💡 长期优化建议

### 1. 连接池自适应调整
- 根据并发度动态调整连接池大小
- 监控连接使用率，自动优化参数

### 2. 上传任务优化
- 实现连接池感知的任务调度
- 添加任务队列限流机制

### 3. 数据库操作优化
- 批量数据库操作
- 减少不必要的数据库查询

---

## 🔍 验证方法

### 1. 连接池状态检查
```python
# 检查连接池配置是否生效
from qianchuan_aw.database.database import engine
print(f"Pool size: {engine.pool.size()}")
print(f"Max overflow: {engine.pool._max_overflow}")
```

### 2. 上传成功率监控
- 监控上传任务成功率
- 记录连接超时错误频率

### 3. 系统资源监控
- 数据库连接数
- 内存使用情况
- CPU负载

---

**结论**: 问题根源是数据库连接池配置未生效 + 高并发设计不合理。通过修复连接池配置和降低并发度，可以彻底解决上传失败问题。
