#!/usr/bin/env python3
"""
同步Playwright管理器
解决异步冲突问题，提供稳定的浏览器自动化
"""

import time
import threading
import atexit
from typing import Optional, Dict, Any
from contextlib import contextmanager
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page
import json
from pathlib import Path
import os

from qianchuan_aw.utils.logger import logger

class SyncPlaywrightManager:
    """同步Playwright管理器"""

    def __init__(self, cookies_path: str = "config/browser_cookies.json"):
        self.cookies_path = Path(cookies_path)
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.playwright = None
        self.lock = threading.Lock()

        # 注册进程退出自动清理
        try:
            atexit.register(self.cleanup)
        except Exception:
            pass

        # 空闲自动回收配置（settings.yml > env > default）
        from qianchuan_aw.utils.config_loader import load_settings
        try:
            cfg = load_settings().get('browser_management', {})
        except Exception:
            cfg = {}
        self._last_used = time.time()
        self._idle_seconds = int(cfg.get('sync_idle_timeout_seconds', int(os.environ.get('QC_SYNC_BROWSER_IDLE_TIMEOUT', '180'))))
        self._gc_thread = threading.Thread(target=self._idle_gc_worker, daemon=True)
        self._gc_thread.start()

        # 浏览器配置
        self.browser_config = {
            'headless': True,
            'args': [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-extensions',
                '--no-first-run',
                '--disable-default-apps'
            ]
        }

        # 上下文配置
        self.context_config = {
            'viewport': {'width': 1920, 'height': 1080},
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }

    def start_browser(self):
        """启动浏览器"""
        with self.lock:
            if self.browser is not None:
                return

            try:
                self.playwright = sync_playwright().start()
                self.browser = self.playwright.chromium.launch(**self.browser_config)

                # 创建上下文
                self.context = self.browser.new_context(**self.context_config)

                # 加载cookies
                self._load_cookies()

                logger.info("✅ 同步Playwright浏览器启动成功")

            except Exception as e:
                logger.error(f"❌ 启动浏览器失败: {e}")
                self.cleanup()
                raise

    def _load_cookies(self):
        """加载cookies"""
        if self.cookies_path.exists():
            try:
                with open(self.cookies_path, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)

                if cookies and self.context:
                    self.context.add_cookies(cookies)
                    logger.info(f"✅ 加载了 {len(cookies)} 个cookies")

            except Exception as e:
                logger.warning(f"⚠️ 加载cookies失败: {e}")

    def _save_cookies(self):
        """保存cookies"""
        if self.context:
            try:
                cookies = self.context.cookies()
                with open(self.cookies_path, 'w', encoding='utf-8') as f:
                    json.dump(cookies, f, ensure_ascii=False, indent=2)
                logger.debug(f"保存了 {len(cookies)} 个cookies")
            except Exception as e:
                logger.warning(f"保存cookies失败: {e}")

    @contextmanager
    def get_page(self, timeout: int = 30000):
        self._last_used = time.time()
        """获取页面上下文管理器"""
        page = None
        try:
            if not self.browser or not self.context:
                self.start_browser()

            page = self.context.new_page()
            page.set_default_timeout(timeout)

            yield page

        except Exception as e:
            logger.error(f"页面操作异常: {e}")
            raise
        finally:
            if page:
                try:
                    page.close()
                except:
                    pass

            # 保存cookies
            self._save_cookies()

    def cleanup(self):
        """清理资源"""
        with self.lock:
            try:
                if self.context:
                    self.context.close()
                    self.context = None

                if self.browser:
                    self.browser.close()
                    self.browser = None

                if self.playwright:
                    self.playwright.stop()
                    self.playwright = None

                logger.info("✅ Playwright资源清理完成")

            except Exception as e:
                logger.error(f"清理Playwright资源失败: {e}")

    def is_healthy(self) -> bool:
        """检查浏览器健康状态"""
        try:
            if not self.browser or not self.context:
                return False

    def _idle_gc_worker(self):
        """空闲回收后台线程"""
        while True:
            try:
                time.sleep(5)
                if self.browser is None:
                    continue
                # 长时间未使用则回收
                if time.time() - self._last_used >= self._idle_seconds:
                    logger.info(f"SyncPlaywrightManager 空闲{self._idle_seconds}s，自动回收浏览器实例")
                    self.cleanup()
            except Exception:
                time.sleep(10)

            # 尝试创建一个测试页面
            with self.get_page(timeout=5000) as page:
                page.goto("about:blank")
                return True

        except Exception:
            return False

    def restart_if_needed(self):
        """如果需要则重启浏览器"""
        if not self.is_healthy():
            logger.warning("检测到浏览器不健康，正在重启...")
            self.cleanup()
            self.start_browser()

# 全局实例
sync_playwright_manager = SyncPlaywrightManager()
